<?php

namespace Modules\FameManagement\Models;

use CodeIgniter\Model;
use GuzzleHttp\Client;
use GuzzleHttp;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Promise\Utils;
use GuzzleHttp\Psr7\Request;

use \Modules\FameManagement\Config\FameValidationApi as ApiConfig;

class GuzzleModel extends Model
{
    protected $DBGroup          = 'fplusDB';
    protected $table            = 'tbl_users';
    protected $primaryKey       = 'uid';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [];


    protected $apiConfig;

    public function __construct()
    {
        parent::__construct();
        $this->apiConfig = new ApiConfig();
    }

    public function guz_patch($data,$email)
    {
        $base_url = $this->apiConfig->baseUrl;
        $endpoint = $this->apiConfig->endpoints['patch'];
        $uri = $base_url.$endpoint.$email;
        // echo $uri;exit();
        $client = new GuzzleHttp\Client(['verify' => false]);
        // print_r($this->apiConfig);exit();
        $headers =  ['headers' => $this->apiConfig->getHeaders()];
        
        $prep_data = array('form_params'=>$data);
        $payload = array_merge($headers,$prep_data);

        // print_r($payload);exit();

        try {
            $response = $client->patch($uri, $payload);
            $status = $response->getStatusCode();
            $body = json_decode($response->getBody()->getContents(),true);
            return $response->getBody()->getContents();
        }
        catch (RequestException $e) {
            throw new \Exception('Request failed: ' . $e->getMessage(), $e->getCode(), $e);
        }

        //test on email: <EMAIL> Phoenix Sun Int'l. Corp./La Bella Lifestyle Properties Inc.
    }

    public function guz_post($payload)
    {
        // print_r(json_encode($payload));exit();
        $base_url = $this->apiConfig->baseUrl;
        $endpoint = $this->apiConfig->endpoints['buyerPost'];
        $uri = $base_url.$endpoint;
        $client = new GuzzleHttp\Client(['verify' => false]);
        $headers =  ['headers' => $this->apiConfig->getHeaders()];
        $data = array('form_params'=>$payload);
        
        $payload = array_merge($headers,$data);
        // print_r($payload);exit();
        try {
            $response = $client->request('POST',$uri, $payload);
            $status = $response->getStatusCode();
            $body = json_decode($response->getBody()->getContents(),true);
            // print_r($body);exit();
            return $response->getBody()->getContents();
        }
        catch (RequestException $e) {
            throw new \Exception('Request failed: ' . $e->getMessage(), $e->getCode(), $e);
        }

    }



}
