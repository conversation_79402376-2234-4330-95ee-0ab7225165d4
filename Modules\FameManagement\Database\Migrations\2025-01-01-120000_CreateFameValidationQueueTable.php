<?php

namespace Modules\FameManagement\Database\Migrations;

use CodeIgniter\Database\Migration;

/**
 * Migration to create the fame_validation_queue table
 * This table stores async validation jobs for the Fame Management module
 */
class CreateFameValidationQueueTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'job_type' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
                'comment' => 'Type of validation job (e.g., fame_validation_patch)'
            ],
            'payload' => [
                'type' => 'TEXT',
                'null' => false,
                'comment' => 'JSON payload containing validation data'
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['pending', 'processing', 'completed', 'failed'],
                'default' => 'pending',
                'null' => false,
                'comment' => 'Current status of the job'
            ],
            'attempts' => [
                'type' => 'TINYINT',
                'constraint' => 3,
                'unsigned' => true,
                'default' => 0,
                'null' => false,
                'comment' => 'Number of processing attempts'
            ],
            'max_attempts' => [
                'type' => 'TINYINT',
                'constraint' => 3,
                'unsigned' => true,
                'default' => 3,
                'null' => false,
                'comment' => 'Maximum number of attempts allowed'
            ],
            'last_error' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Last error message if job failed'
            ],
            'result' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'JSON result data from successful processing'
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
                'comment' => 'When the job was created'
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => false,
                'comment' => 'When the job was last updated'
            ],
            'completed_at' => [
                'type' => 'DATETIME',
                'null' => true,
                'comment' => 'When the job was completed'
            ],
            'scheduled_at' => [
                'type' => 'DATETIME',
                'null' => true,
                'comment' => 'When the job should be processed (for delayed jobs)'
            ]
        ]);

        // Set primary key
        $this->forge->addPrimaryKey('id');

        // Add indexes for performance
        $this->forge->addKey(['status', 'attempts'], false, false, 'idx_status_attempts');
        $this->forge->addKey('job_type', false, false, 'idx_job_type');
        $this->forge->addKey('created_at', false, false, 'idx_created_at');
        $this->forge->addKey('scheduled_at', false, false, 'idx_scheduled_at');

        // Create the table
        $this->forge->createTable('fame_validation_queue');

        // Add some sample data for testing
        $this->db->table('fame_validation_queue')->insertBatch([
            [
                'job_type' => 'fame_validation_patch',
                'payload' => json_encode([
                    'user_id' => 1001,
                    'email' => '<EMAIL>',
                    'status' => 'pending',
                    'validation_type' => 'patch_validation',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'metadata' => [
                        'fair_code' => 'MFIO2025',
                        'sector' => '02',
                        'source' => 'fame_management'
                    ]
                ]),
                'status' => 'pending',
                'attempts' => 0,
                'max_attempts' => 3,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'scheduled_at' => date('Y-m-d H:i:s')
            ],
            [
                'job_type' => 'fame_validation_patch',
                'payload' => json_encode([
                    'user_id' => 1002,
                    'email' => '<EMAIL>',
                    'status' => 'approved',
                    'validation_type' => 'patch_validation',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'metadata' => [
                        'fair_code' => 'MFIO2025',
                        'sector' => '02',
                        'source' => 'fame_management'
                    ]
                ]),
                'status' => 'completed',
                'attempts' => 1,
                'max_attempts' => 3,
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 hour')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-30 minutes')),
                'completed_at' => date('Y-m-d H:i:s', strtotime('-30 minutes')),
                'scheduled_at' => date('Y-m-d H:i:s', strtotime('-1 hour'))
            ]
        ]);
    }

    public function down()
    {
        $this->forge->dropTable('fame_validation_queue');
    }
}
