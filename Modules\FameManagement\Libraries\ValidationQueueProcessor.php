<?php

namespace Modules\FameManagement\Libraries;

use Modules\FameManagement\Config\FameValidationApi;
use Modules\FameManagement\Models\ValidationQueueModel;

/**
 * Validation Queue Processor
 * 
 * Handles asynchronous processing of Fame Management validation requests
 * This processor can be called from CLI commands, cron jobs, or web triggers
 */
class ValidationQueueProcessor
{
    protected $config;
    protected $queueModel;

    public function __construct()
    {
        $this->config = new FameValidationApi();
        $this->queueModel = new ValidationQueueModel();
    }

    /**
     * Process pending validation requests from the queue
     * 
     * @param int $batchSize Number of items to process in this batch
     * @return array Processing results
     */
    public function processPendingValidations(int $batchSize = 10): array
    {
        $results = [];
        
        // Get pending validation requests
        $pendingJobs = $this->queueModel->getPendingJobs($batchSize);
        
        if (empty($pendingJobs)) {
            return ['message' => 'No pending validation jobs to process'];
        }

        foreach ($pendingJobs as $job) {
            $result = $this->processValidationJob($job);
            $results[] = $result;
            
            // Update job status in database
            $this->updateJobStatus($job['id'], $result);
        }

        return $results;
    }

    /**
     * Process a single validation job
     * 
     * @param array $job Job data from queue
     * @return array Processing result
     */
    protected function processValidationJob(array $job): array
    {
        try {
            // Load GuzzleHTTP helper
            helper('Modules\GuzzleHttp\Helpers\guzzle');
            
            // Get the configured endpoint
            $guzzle = http_endpoint('fame_validation');
            
            // Decode the payload
            $payload = json_decode($job['payload'], true);
            
            if (!$payload) {
                throw new \Exception('Invalid payload format');
            }

            // Make the API request
            $response = $guzzle->post($this->config->endpoints['validate'], $payload);
            
            if ($response['success']) {
                log_message('info', "Validation job {$job['id']} completed successfully");
                
                return [
                    'job_id' => $job['id'],
                    'status' => 'completed',
                    'message' => 'Validation processed successfully',
                    'api_response' => $response['data'],
                    'processed_at' => date('Y-m-d H:i:s')
                ];
            } else {
                throw new \Exception('API request failed: ' . ($response['message'] ?? 'Unknown error'));
            }
            
        } catch (\Exception $e) {
            log_message('error', "Validation job {$job['id']} failed: " . $e->getMessage());
            
            return [
                'job_id' => $job['id'],
                'status' => 'failed',
                'message' => $e->getMessage(),
                'error_at' => date('Y-m-d H:i:s')
            ];
        }
    }

    /**
     * Update job status in the database
     * 
     * @param int $jobId Job ID
     * @param array $result Processing result
     */
    protected function updateJobStatus(int $jobId, array $result): void
    {
        $updateData = [
            'status' => $result['status'],
            'attempts' => $this->queueModel->incrementAttempts($jobId),
            'last_error' => $result['status'] === 'failed' ? $result['message'] : null,
            'completed_at' => $result['status'] === 'completed' ? date('Y-m-d H:i:s') : null,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $this->queueModel->update($jobId, $updateData);
    }

    /**
     * Retry failed jobs that haven't exceeded max attempts
     * 
     * @return array Retry results
     */
    public function retryFailedJobs(): array
    {
        $failedJobs = $this->queueModel->getRetryableJobs($this->config->queueConfig['max_attempts']);
        $results = [];

        foreach ($failedJobs as $job) {
            // Reset status to pending for retry
            $this->queueModel->update($job['id'], [
                'status' => 'pending',
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            $results[] = [
                'job_id' => $job['id'],
                'action' => 'queued_for_retry',
                'attempt' => $job['attempts'] + 1
            ];
        }

        return $results;
    }

    /**
     * Clean up old completed/failed jobs
     * 
     * @param int $daysOld Number of days old to consider for cleanup
     * @return int Number of jobs cleaned up
     */
    public function cleanupOldJobs(int $daysOld = 30): int
    {
        return $this->queueModel->deleteOldJobs($daysOld);
    }

    /**
     * Get queue statistics
     * 
     * @return array Queue statistics
     */
    public function getQueueStats(): array
    {
        return [
            'pending' => $this->queueModel->countByStatus('pending'),
            'processing' => $this->queueModel->countByStatus('processing'),
            'completed' => $this->queueModel->countByStatus('completed'),
            'failed' => $this->queueModel->countByStatus('failed'),
            'total' => $this->queueModel->countAll()
        ];
    }
}
