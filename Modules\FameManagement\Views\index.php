<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
<?= $page_title ?>
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item active">User Management</li>
<?= $this->endSection() ?>


<?= $this->section('header'); ?>
<style>
    .max-width-column-150 {
        white-space: normal !important;
        word-wrap: break-word !important;
        max-width: 150px;
        text-align: center!important;
    }
    .max-width-column-350 {
        white-space: normal !important;
        word-wrap: break-word !important;
        max-width: 350px;
        text-align: center!important;
    }
    .table-compact {
        font-size: 0.875rem;
    }
    .btn-group .btn {
        margin-right: 0.25rem;
    }
    .btn-group .btn:last-child {
        margin-right: 0;
    }
</style>
<?= $this->endSection(); ?>

<?= $this->section('content'); ?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Fame Management Profile</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h4 class="card-title mb-0">Statistics</h4>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-6">
                                        <div class="text-center">
                                            <div class="h2 text-primary"><?= $fameplus_count ?></div>
                                            <div class="text-muted">Total FamePlus</div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="text-center">
                                            <div class="h2 text-success"><?= $citem_total ?></div>
                                            <div class="text-muted">Total CITEM</div>
                                        </div>
                                    </div>
                                </div>
                                <hr>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Status</th>
                                                <th class="text-end">Count</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach($status_count as $row): ?>
                                            <tr>
                                                <td><?= $row['validation_status'] ?></td>
                                                <td class="text-end">
                                                    <span class="badge bg-secondary"><?= $row['total'] ?></span>
                                                </td>
                                            </tr>
                                            <?php endforeach;?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="alert alert-info">
                            <h4 class="alert-heading">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                    <path d="M12 9v4"/>
                                    <path d="M10.363 3.591l-8.106 13.534a1.914 1.914 0 0 0 1.636 2.871h16.214a1.914 1.914 0 0 0 1.636 -2.87l-8.106 -13.536a1.914 1.914 0 0 0 -3.274 0z"/>
                                    <path d="M12 16h.01"/>
                                </svg>
                                Notes:
                            </h4>
                            <ul class="mb-0">
                                <li>Lorem Ipsum</li>
                                <li>Lorem Ipsum</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table id="profile_table" class="table table-striped table-hover table-compact" style="width:100%">
                        <thead>
                            <tr>
                                <th>FamePlus</th>
                                <th>FamePlus Status</th>
                                <th>CITEM</th>
                                <th>CITEM Status</th>
                                <th class="max-width-column-20">Fair Codes</th>
                                <th>Remarks</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                    <tbody>
                      <?php foreach ($data as $row) : ?>
                        <?php 
                        $fair_codes = $row['fair_codes']??'';
                        $fair_codes = str_replace(',', '<br>', $fair_codes);
                        $validation_status = $row['validation_status']??'';
                        $co_name = $row['co_name']??'';
                        $citem_email = $row['citem_email']??'';
                        ?>  
                        
                        <tr>
                          <td><?= $row['fplus_email'].'<br>'.$row['fplus_company'] ?></td>
                          <td><?= $row['fplus_status'] ?></td>
                          <td><?= $citem_email.'<br>'.$co_name ?></td>
                          <td>
                                <?php if ($row['fplus_status'] != $validation_status): ?>
                                    <span class="badge bg-danger">ALERT</span><br>
                                <?php endif; ?>
                                <span class="badge bg-<?= !empty($validation_status) ? 'success' : 'secondary' ?>">
                                    <?= $row['validation_status'] ?? 'No Status' ?>
                                </span>
                            </td>
                          <td><?= $fair_codes??'' ?></td>
                            <td>
                                <?php if (isset($row['citem_email'])) : ?>
                                    <span class="badge bg-success">Have CITEMDB Record</span><br>
                                    <?php if($row['fplus_id']==$row['fameplus_id']): ?>
                                        <span class="badge bg-success mt-1">FPLUSID MATCHED</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger mt-1">ALERT FPLUSID UNMATCHED (<?=$row['fplus_id'].'|'.$row['fameplus_id']?>)</span>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="badge bg-danger">ALERT No CITEMDB Record</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-info post-btn"
                                            data-id="<?= $row['fplus_id'] ?>"
                                            data-email="<?= $row['fplus_email']?>"
                                            title="API POST">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <path d="M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2"/>
                                            <path d="M7 9l5 -5l5 5"/>
                                            <path d="M12 4l0 12"/>
                                        </svg>
                                        POST
                                    </button>
                                    <button class="btn btn-sm btn-warning btn-validate-patch"
                                            data-id="<?= $row['fplus_id'] ?>"
                                            data-email="<?= $row['fplus_email']?>"
                                            data-validation-status="<?= $row['fplus_validation_status'] ?>"
                                            title="Validate">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <path d="M5 12l5 5l10 -10"/>
                                        </svg>
                                        VALIDATE
                                    </button>
                                </div>
                            </td>
                        </tr>
                      <?php endforeach;?>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>




<?= $this->endSection(); ?>

<?= $this->section("script") ?>
<!-- Additional plugins for validation and input masking -->
<script src="<?= base_url("assets/")?>plugins/jquery-validation/jquery.validate.min.js"></script>
<script src="<?= base_url("assets/")?>plugins/inputmask/jquery.inputmask.js"></script>

<script>

$(document).ready(function() {
  var base_url = '<?= base_url(); ?>';

  // Initialize DataTable with Bootstrap 5 styling
  var profileTable = $('#profile_table').DataTable({
      paging: true,
      ordering: false,
      responsive: true,
      lengthChange: true,
      autoWidth: false,
      pageLength: 25,
      lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
      language: {
          search: "Search:",
          lengthMenu: "Show _MENU_ entries",
          info: "Showing _START_ to _END_ of _TOTAL_ entries",
          infoEmpty: "Showing 0 to 0 of 0 entries",
          infoFiltered: "(filtered from _MAX_ total entries)",
          paginate: {
              first: "First",
              last: "Last",
              next: "Next",
              previous: "Previous"
          }
      },
      dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
           '<"row"<"col-sm-12"tr>>' +
           '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
      columnDefs: [
          { targets: [0, 2], className: "max-width-column-150" },
          { targets: [4], className: "max-width-column-350" },
          { targets: [-1], orderable: false, searchable: false }
      ]
  });


  // $('#profile_table').on('click','.post-btn',function(){
  //   event.preventDefault();
  //   var id = $(this).data('id');
  //   var method = 'post';
  //   console.log("ID: " + id);

    
  //   // Disable the button and show processing state
  //   var $btn = $(this);
  //   $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');

  //   alert("Email: " + email + "\nPayload: " + payload);
    
  //   $.ajax({
  //       dataType: 'json',
  //       type:'post',
  //       url: '<?//= site_url('/admin/fame/guzzlehttp')?>',
  //       data: {email: email,method:method},
  //       cache: false,
  //       beforeSend: function(){
  //               // $('.loader').show();
  //           },
  //       success: function(response) {
  //         console.log(response);
  //         if (response.status=='success') {
  //           Swal.fire({
  //             title: "Saved!",
  //             text: response.status+': '+response.message,
  //             icon: "success",
  //           });
  //         // salesTable.draw();
  //         // profileTable.ajax.reload();
  //         // Update the table row or reload the page
  //       } else {
  //         Swal.fire({
  //             title: response.status,
  //             text: response.status+': '+response.message,
  //             icon: "error",
  //           });
  //       }
  //     },
  //     error: function(xhr, status, error) {
  //       console.log('error');
  //     },
  //     complete: function() {
  //       // Re-enable the button and restore original text
  //       $btn.prop('disabled', false).html('<i class="fas fa-upload"></i> POST');
  //     }
  //   });
  // });

  // $('#profile_table').on('click','.patch-btn',function(){
  //   event.preventDefault();

  //   var targetRow = $(this).closest('tr');
  //   var rowData = $('#profile_table').DataTable().row(targetRow).data();
  //   var email = rowData.email;
  //   var method = 'patch';
  //   var payload = rowData.payload;
  //   console.log("Email: " + email);
  //   console.log("Payload: " + payload);
  //   return;
  //   alert("Email: " + email + "\nPayload: " + payload);
  //   $.ajax({
  //       dataType: 'json',
  //       type:'post',
  //       url: '<?//= site_url('/ifex/guzzlehttp')?>',
  //       data: {email: email,payload:payload,method:method},
  //       cache: false,
  //       beforeSend: function(){
  //               // $('.loader').show();
  //           },
  //       success: function(response) {
  //         if (response.status=='success') {
  //           Swal.fire({
  //             title: "Saved!",
  //             text: response.status+': '+response.message,
  //             icon: "success",
  //           });
  //         // salesTable.draw();
  //         // inquiryTable.ajax.reload();
  //         // $('#'+modalId).modal('hide');
  //         // activateTab('inquiry-content-tab');
  //         // Update the table row or reload the page
  //       } else {
  //         Swal.fire({
  //             title: response.status,
  //             text: response.status+': '+response.message,
  //             icon: "success",
  //           });
  //       }
  //     },
  //     error: function(xhr, status, error) {
  //       console.error('error');
  //     }
  //   });
  // });

  // $('#profile_table').on('click','.validate-patch-btn',function()
  // {
  //   event.preventDefault();
  //   //debug
  //   console.log('validate patch');
  //   var id = $(this).data('id');
  //   var email = $(this).data('email');
  //   // var email = $(this).closest('tr').find('td:first').text();
  //   var status = $(this).closest('tr').find('td:nth-child(2)').text();
  //   var method = 'validatepatch';
  //   console.log("ID: " + id);
  //   console.log("Email: " + email);
  //   console.log("Status: " + status);
  //   return;
  //   var $btn = $(this);
  //   $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');

  //   $.ajax({
  //       dataType: 'json',
  //       type:'post',
  //       url: '<?//= site_url('/admin/fame/guzzlehttp')?>',
  //       data: {email: email,status:status,method:method},
  //       cache: false,
  //       beforeSend: function(){
  //               // $('.loader').show();
  //           },
  //       success: function(response) {
  //         if (response.status=='success') {
  //           Swal.fire({
  //             title: response.status,
  //             text: response.status+': '+response.message,
  //             icon: "success",
  //           });
  //         // salesTable.draw();
  //         // inquiryTable.ajax.reload();
  //         // $('#'+modalId).modal('hide');
  //         // activateTab('inquiry-content-tab');
  //         // Update the table row or reload the page
  //       } else {
  //         Swal.fire({
  //             title: response.status,
  //             text: response.status+': '+response.message,
  //             icon: "error",
  //           });
  //       }
  //     },
  //     error: function(xhr, status, error) {
  //       console.error('error');
  //     },
  //     complete: function() {
  //       // Re-enable the button and restore original text
  //       $btn.prop('disabled', false).html('<i class="fas fa-check-circle"></i> VALIDATE');
  //     }
  //   });
  // });


initializePostButtons();
initializeValidationButtons();


});

function initializePostButtons() {
    // POST button click handler
    $(document).on('click', '.post-btn', function() {
      const $btn = $(this);
      const originalText = $btn.html();
        const row = $(this).closest('tr');
        const table = $('#profile_table').DataTable();
        const rowData = table.row(row).data();
        const id = $(this).data('id');
        const email = $(this).data('email');
        const method = 'post';

        Swal.fire({
            title: 'Confirm POST',
            text: `Send POST request for ${email}?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, Send',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
              disableButton($btn);
                sendPost(id, email, method, $btn, originalText);
            }
        });
    });
}

function sendPost(id, email, method, $btn, originalText) {
    const data = {
        id: id,
        email: email,
        method: method
    };
    $.ajax({
        dataType: 'json',
        type: 'POST',
        url: '/admin/fame/buyer/api-post',
        data: data,
        cache: false,
        beforeSend: function() {

            // Show loading indicator
            // showLoadingIndicator('Processing POST request...');
        },
        success: function(response) {

            hideLoadingIndicator();
            if (response.status === 'success') {
              enableButton($btn, originalText);
                //sweet alert toast
                Swal.fire({
                  toast: true,
                  position: 'top-end', // or 'top-start', 'bottom-end', etc.
                  icon: 'success',     // 'success', 'error', 'warning', 'info', or 'question'
                  title: response.message,
                  showConfirmButton: false,
                  timer: 3000,         // Auto-close after 3 seconds
                  timerProgressBar: true,
                  didOpen: (toast) => {
                    toast.addEventListener('mouseenter', Swal.stopTimer)
                    toast.addEventListener('mouseleave', Swal.resumeTimer)
                  }
                });

                // showSuccessMessage(
                //     'POST Request Sent',
                //     `${response.message}<br>
                //     <strong>Reference ID:</strong> ${response.data.reference_id}<br>
                //     <strong>Job ID:</strong> ${response.data.job_id}<br>
                //     <strong>Estimated Processing:</strong> ${response.data.estimated_processing_time}`
                // );
            } else {
              enableButton($btn, originalText);
                showErrorMessage('POST Request Failed', response.message);
            }
        },
        error: function(xhr, status, error) {
            hideLoadingIndicator();
            showErrorMessage('Request Failed', 'An error occurred while processing the request.');
            enableButton($btn, originalText);
        }
    });
}
        

function initializeValidationButtons() {
    // Validation patch button click handler
    $(document).on('click', '.btn-validate-patch', function() {
        const $btn = $(this);
        const originalText = $btn.html();

        const row = $(this).closest('tr');
        const table = $('#profile_table').DataTable();
        const rowData = table.row(row).data();
        const id = $(this).data('id'); 
        const email = $(this).data('email');
        const validation_status = $(this).data('validation-status'); 
        // const validation_status = rowData[1];
        const status = 'pending';

        
        // Confirm before sending
        Swal.fire({
            title: 'Confirm Validation',
            text: `Send validation patch request for ${email}?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, Send',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
              disableButton($btn);
              sendPatch(email, status, validation_status, id, $btn, originalText);
            }
        });
    });
}

function sendPatch(email, status = 'pending', validation_status, id = null, $btn, originalText) {
    const data = {
        email: email,
        status: status,
        id: id,
        method: 'patch',
        validation_status: validation_status
    };

    return $.ajax({
        dataType: 'json',
        type: 'POST',
        url: '/admin/fame/buyer/api-patch',
        data: data,
        cache: false,
        beforeSend: function() {
            // Show loading indicator
            // showLoadingIndicator('Processing validation request...');
        },
        success: function(response) {
            // hideLoadingIndicator();
            
            if (response.status === 'success') {
              enableButton($btn, originalText);
                Swal.fire({
                  toast: true,
                  position: 'top-end', // or 'top-start', 'bottom-end', etc.
                  icon: 'success',     // 'success', 'error', 'warning', 'info', or 'question'
                  title: response.message,
                  showConfirmButton: false,
                  timer: 3000,         // Auto-close after 3 seconds
                  timerProgressBar: true,
                  didOpen: (toast) => {
                    toast.addEventListener('mouseenter', Swal.stopTimer)
                    toast.addEventListener('mouseleave', Swal.resumeTimer)
                  }
                });
                // showSuccessMessage(
                //     'Validation Queued',
                //     `${response.message}<br>
                //     <strong>Reference ID:</strong> ${response.data.reference_id}<br>
                //     <strong>Job ID:</strong> ${response.data.job_id}<br>
                //     <strong>Estimated Processing:</strong> ${response.data.estimated_processing_time}`
                // );
                
                // Optionally poll for status updates
                // if (response.data.job_id) {
                //     pollJobStatus(response.data.job_id);
                // }
            } else {
                showErrorMessage('Validation Failed', response.message);
                enableButton($btn, originalText);
            }
        },
        error: function(xhr, status, error) {
            hideLoadingIndicator();
            showErrorMessage('Request Failed', 'An error occurred while processing the validation request.');
            console.error('Validation request error:', error);
            enableButton($btn, originalText);
        }
    });
}





/**
 * Utility functions for UI feedback
 */

function disableButton($btn) {
    $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');
}

function enableButton($btn, originalText) {
    $btn.prop('disabled', false).html(originalText);
}

function showLoadingIndicator(message = 'Processing...') {
    Swal.fire({
        title: 'Processing',
        html: message,
        allowOutsideClick: false,
        didOpen: () => {
          swal.showLoading()
        }
      })
}

function hideLoadingIndicator() {
    Swal.close();
}

function showSuccessMessage(title, message) {
    Swal.fire({
        title: title,
        html: message,
        icon: 'success',
        confirmButtonText: 'OK'
    });
}

function showErrorMessage(title, message) {
    Swal.fire({
        title: title,
        text: message,
        icon: 'error',
        confirmButtonText: 'OK'
    });
}

function showWarningMessage(title, message) {
    Swal.fire({
        title: title,
        text: message,
        icon: 'warning',
        confirmButtonText: 'OK'
    });
}

</script>

<?= $this->endSection(); ?>
