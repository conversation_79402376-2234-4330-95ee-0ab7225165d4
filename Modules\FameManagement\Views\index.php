<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
<?= $page_title ?>
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item active">User Management</li>
<?= $this->endSection() ?>


<?= $this->section('header'); ?>
  <!-- DataTables -->
  <!-- DataTables -->
  <link rel="stylesheet" href="<?php echo base_url("assets/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css"); ?>">
  <link rel="stylesheet" href="<?php echo base_url("assets/plugins/datatables-responsive/css/responsive.bootstrap4.min.css"); ?>">
  <link rel="stylesheet" href="<?php echo base_url("assets/plugins/datatables-buttons/css/buttons.bootstrap4.min.css"); ?>">

<style>
      .max-width-column-150 {
      white-space: normal !important;
      word-wrap: break-word !important;
      max-width: 150px;
      text-align: center!important;
/*      overflow: hidden;*/
/*      text-overflow: ellipsis;*/
    }
    .max-width-column-350 {
      white-space: normal !important;
      word-wrap: break-word !important;
      max-width: 350px;
      text-align: center!important;
/*      overflow: hidden;*/
/*      text-overflow: ellipsis;*/
    }
</style>
<?= $this->endSection(); ?>

<?= $this->section('content'); ?>

        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-body">

                <h5 class="card-title">PROFILE</h5>
 
                <p class="card-text">
                  <div class="row">
                    <div class="col-md-6">
                      <div class="card card-outline card-primary">
                        <div class="card-header">
                          <h3 class="card-title text-bold">Stats</h3>
                        </div>
                        <div class="card-body">
                          <p class="card-text">Total FamePlus: <?= $fameplus_count ?></p>
                          <p class="card-text">Total CITEM: <?= $citem_total ?></p>
                          <table class="table table-sm table-borderless">
                            <tbody>
                              <?php foreach($status_count as $row): ?>
                                
                                  <tr>
                                    <td><?= $row['validation_status'] ?></td>
                                    <td><?= $row['total'] ?></td>
                                  </tr>
                                

                              <?php endforeach;?>
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>

                    <div class="col-md-6">
                      <div class="alert alert-info">
                          <h5><i class="fas fa-info"></i> Notes:</h5>
                          <ul class="mb-0">
                              <li>Lorem Ipsum</li>
                              <li>Lorem Ipsum</li>
                          </ul>
                      </div>
                    </div>

                  </div>

                  <!-- <div class="container" style="max-width:1200px"> -->
                  <table id="profile_table" class=" table-bordered table-striped display compact cell-border" style="width:100%">
                    <thead>
                      <tr>
                        <!-- <th>#</th> -->
                        <th>fplus</th>
                        <th>fplus status</th>
                        <th>citem</th>
                        <th>citem status</th>
                        <th class="max-width-column-20">fair codes</th>
                        <th>remarks</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      <?php foreach ($data as $row) : ?>
                        <?php 
                        $fair_codes = $row['fair_codes']??'';
                        $fair_codes = str_replace(',', '<br>', $fair_codes);
                        $validation_status = $row['validation_status']??'';
                        $co_name = $row['co_name']??'';
                        $citem_email = $row['citem_email']??'';
                        ?>  
                        
                        <tr>
                          <td><?= $row['fplus_email'].'<br>'.$row['fplus_company'] ?></td>
                          <td><?= $row['fplus_status'] ?></td>
                          <td><?= $citem_email.'<br>'.$co_name ?></td>
                          <td><?= ($row['fplus_status']!=$validation_status)? '<span style="color:red;">ALERT</span>' : '' ?>
                          <?= $row['validation_status']??'' ?>
                        
                        </td>
                          <td><?= $fair_codes??'' ?></td>
                          <td>
                            <?php if (isset($row['citem_email'])) : ?>
                              Have CITEMDB Record</br>
                              <?php if($row['fplus_id']==$row['fameplus_id']): ?>
                                FPLUSID MATCHED
                              <?php else: ?>
                                <span style="color:red;">ALERT FPLUSID UNMATCHED (<?=$row['fplus_id'].'|'.$row['fameplus_id']?>)</span>
                                
                              <?php endif; ?>
                            <?php else: ?>
                              <span style="color:red;">ALERT No CITEMDB Record</span>
                              
                            <?php endif; ?>
                          </td>
                          <td>

                            <div class="btn-group">
                              <button class="btn btn-sm btn-info post-btn" data-id="<?= $row['fplus_id'] ?>" data-email="<?= $row['fplus_email']?>" title="API POST">
                                <i class="fas fa-upload"></i> POST
                              </button>
                              <!-- <button class="btn btn-sm btn-primary patch-btn" data-id="<?//= $row['ifexconn_id'] ?>" title="API PATCH">
                                <i class="fas fa-edit"></i> PATCH
                              </button> -->
                              <button class="btn btn-sm btn-warning btn-validate-patch" data-id="<?= $row['fplus_id'] ?>" data-email="<?= $row['fplus_email']?>" data-validation-status="<?= $row['fplus_validation_status'] ?>" title="Validate">
                                <i class="fas fa-check-circle"></i> VALIDATE
                              </button>
                            </div>

                          </td>
                        </tr>
                      <?php endforeach;?>

                    </tbody>
                  </table>
                <!-- </div> -->

                </p>
                <!-- <a href="#" class="card-link">Card link</a> -->
                <!-- <a href="#" class="card-link">Another link</a> -->
              </div>
            </div>
          </div>
        </div>
        <!-- /.row -->




<?= $this->endSection(); ?>

<?= $this->section("script") ?>
<!-- DataTables  & Plugins -->
<script src="<?php echo base_url("assets/plugins/datatables/jquery.dataTables.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/datatables-responsive/js/dataTables.responsive.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/datatables-responsive/js/responsive.bootstrap4.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/datatables-buttons/js/dataTables.buttons.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/datatables-buttons/js/buttons.bootstrap4.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/jszip/jszip.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/pdfmake/pdfmake.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/pdfmake/vfs_fonts.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/datatables-buttons/js/buttons.html5.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/datatables-buttons/js/buttons.print.min.js"); ?>"></script>
<script src="<?php echo base_url("assets/plugins/datatables-buttons/js/buttons.colVis.min.js"); ?>"></script>
<script src="<?= base_url("assets/")?>plugins/jquery-validation/jquery.validate.min.js"></script>
<script src="<?= base_url("assets/")?>plugins/inputmask/jquery.inputmask.js"></script>

<script>

$(document).ready(function() {
  var base_url = '<?= base_url(); ?>';

  //for the datatable listing
  var profileTable = $('#profile_table').DataTable({
      // paging: true,
      ordering: false,
      // responsive: true,
      // lengthChange: true,
      autoWidth: true,
      // search: { return: true },
      // serverSide: true,
      // ajax:{
      //   url: '<?php //echo site_url('/ifex/consolidated-data')?>',
      // },
      // columns: [
        // { data: '#', orderable: false},
          // { data: 'email',"className": "max-width-column-150" },
          // { data: 'co_name',"className": "max-width-column-150"},
          // { data: 'payload', "className": "max-width-column-350" },
          // { data: 'country', "className": "max-width-column-170" },
          // { data: 'cost' },
          // { data: 'updated_cost', "className": "sale-updates-cell" },
          // { data: 'status', "className": "max-width-column-80" },
          // { data: 'updated_status', "className": "sale-updates-cell"},
        // { data: 'original_date' },
        // { data: 'updated_date' },
          // { data: 'action' },
      // ],
    });


  // $('#profile_table').on('click','.post-btn',function(){
  //   event.preventDefault();
  //   var id = $(this).data('id');
  //   var method = 'post';
  //   console.log("ID: " + id);

    
  //   // Disable the button and show processing state
  //   var $btn = $(this);
  //   $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');

  //   alert("Email: " + email + "\nPayload: " + payload);
    
  //   $.ajax({
  //       dataType: 'json',
  //       type:'post',
  //       url: '<?//= site_url('/admin/fame/guzzlehttp')?>',
  //       data: {email: email,method:method},
  //       cache: false,
  //       beforeSend: function(){
  //               // $('.loader').show();
  //           },
  //       success: function(response) {
  //         console.log(response);
  //         if (response.status=='success') {
  //           Swal.fire({
  //             title: "Saved!",
  //             text: response.status+': '+response.message,
  //             icon: "success",
  //           });
  //         // salesTable.draw();
  //         // profileTable.ajax.reload();
  //         // Update the table row or reload the page
  //       } else {
  //         Swal.fire({
  //             title: response.status,
  //             text: response.status+': '+response.message,
  //             icon: "error",
  //           });
  //       }
  //     },
  //     error: function(xhr, status, error) {
  //       console.log('error');
  //     },
  //     complete: function() {
  //       // Re-enable the button and restore original text
  //       $btn.prop('disabled', false).html('<i class="fas fa-upload"></i> POST');
  //     }
  //   });
  // });

  // $('#profile_table').on('click','.patch-btn',function(){
  //   event.preventDefault();

  //   var targetRow = $(this).closest('tr');
  //   var rowData = $('#profile_table').DataTable().row(targetRow).data();
  //   var email = rowData.email;
  //   var method = 'patch';
  //   var payload = rowData.payload;
  //   console.log("Email: " + email);
  //   console.log("Payload: " + payload);
  //   return;
  //   alert("Email: " + email + "\nPayload: " + payload);
  //   $.ajax({
  //       dataType: 'json',
  //       type:'post',
  //       url: '<?//= site_url('/ifex/guzzlehttp')?>',
  //       data: {email: email,payload:payload,method:method},
  //       cache: false,
  //       beforeSend: function(){
  //               // $('.loader').show();
  //           },
  //       success: function(response) {
  //         if (response.status=='success') {
  //           Swal.fire({
  //             title: "Saved!",
  //             text: response.status+': '+response.message,
  //             icon: "success",
  //           });
  //         // salesTable.draw();
  //         // inquiryTable.ajax.reload();
  //         // $('#'+modalId).modal('hide');
  //         // activateTab('inquiry-content-tab');
  //         // Update the table row or reload the page
  //       } else {
  //         Swal.fire({
  //             title: response.status,
  //             text: response.status+': '+response.message,
  //             icon: "success",
  //           });
  //       }
  //     },
  //     error: function(xhr, status, error) {
  //       console.error('error');
  //     }
  //   });
  // });

  // $('#profile_table').on('click','.validate-patch-btn',function()
  // {
  //   event.preventDefault();
  //   //debug
  //   console.log('validate patch');
  //   var id = $(this).data('id');
  //   var email = $(this).data('email');
  //   // var email = $(this).closest('tr').find('td:first').text();
  //   var status = $(this).closest('tr').find('td:nth-child(2)').text();
  //   var method = 'validatepatch';
  //   console.log("ID: " + id);
  //   console.log("Email: " + email);
  //   console.log("Status: " + status);
  //   return;
  //   var $btn = $(this);
  //   $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');

  //   $.ajax({
  //       dataType: 'json',
  //       type:'post',
  //       url: '<?//= site_url('/admin/fame/guzzlehttp')?>',
  //       data: {email: email,status:status,method:method},
  //       cache: false,
  //       beforeSend: function(){
  //               // $('.loader').show();
  //           },
  //       success: function(response) {
  //         if (response.status=='success') {
  //           Swal.fire({
  //             title: response.status,
  //             text: response.status+': '+response.message,
  //             icon: "success",
  //           });
  //         // salesTable.draw();
  //         // inquiryTable.ajax.reload();
  //         // $('#'+modalId).modal('hide');
  //         // activateTab('inquiry-content-tab');
  //         // Update the table row or reload the page
  //       } else {
  //         Swal.fire({
  //             title: response.status,
  //             text: response.status+': '+response.message,
  //             icon: "error",
  //           });
  //       }
  //     },
  //     error: function(xhr, status, error) {
  //       console.error('error');
  //     },
  //     complete: function() {
  //       // Re-enable the button and restore original text
  //       $btn.prop('disabled', false).html('<i class="fas fa-check-circle"></i> VALIDATE');
  //     }
  //   });
  // });


initializePostButtons();
initializeValidationButtons();


});

function initializePostButtons() {
    // POST button click handler
    $(document).on('click', '.post-btn', function() {
      const $btn = $(this);
      const originalText = $btn.html();
        const row = $(this).closest('tr');
        const table = $('#profile_table').DataTable();
        const rowData = table.row(row).data();
        const id = $(this).data('id');
        const email = $(this).data('email');
        const method = 'post';

        Swal.fire({
            title: 'Confirm POST',
            text: `Send POST request for ${email}?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, Send',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
              disableButton($btn);
                sendPost(id, email, method, $btn, originalText);
            }
        });
    });
}

function sendPost(id, email, method, $btn, originalText) {
    const data = {
        id: id,
        email: email,
        method: method
    };
    $.ajax({
        dataType: 'json',
        type: 'POST',
        url: '/admin/fame/buyer/api-post',
        data: data,
        cache: false,
        beforeSend: function() {

            // Show loading indicator
            // showLoadingIndicator('Processing POST request...');
        },
        success: function(response) {

            hideLoadingIndicator();
            if (response.status === 'success') {
              enableButton($btn, originalText);
                //sweet alert toast
                Swal.fire({
                  toast: true,
                  position: 'top-end', // or 'top-start', 'bottom-end', etc.
                  icon: 'success',     // 'success', 'error', 'warning', 'info', or 'question'
                  title: response.message,
                  showConfirmButton: false,
                  timer: 3000,         // Auto-close after 3 seconds
                  timerProgressBar: true,
                  didOpen: (toast) => {
                    toast.addEventListener('mouseenter', Swal.stopTimer)
                    toast.addEventListener('mouseleave', Swal.resumeTimer)
                  }
                });

                // showSuccessMessage(
                //     'POST Request Sent',
                //     `${response.message}<br>
                //     <strong>Reference ID:</strong> ${response.data.reference_id}<br>
                //     <strong>Job ID:</strong> ${response.data.job_id}<br>
                //     <strong>Estimated Processing:</strong> ${response.data.estimated_processing_time}`
                // );
            } else {
              enableButton($btn, originalText);
                showErrorMessage('POST Request Failed', response.message);
            }
        },
        error: function(xhr, status, error) {
            hideLoadingIndicator();
            showErrorMessage('Request Failed', 'An error occurred while processing the request.');
            enableButton($btn, originalText);
        }
    });
}
        

function initializeValidationButtons() {
    // Validation patch button click handler
    $(document).on('click', '.btn-validate-patch', function() {
        const $btn = $(this);
        const originalText = $btn.html();

        const row = $(this).closest('tr');
        const table = $('#profile_table').DataTable();
        const rowData = table.row(row).data();
        const id = $(this).data('id'); 
        const email = $(this).data('email');
        const validation_status = $(this).data('validation-status'); 
        // const validation_status = rowData[1];
        const status = 'pending';

        
        // Confirm before sending
        Swal.fire({
            title: 'Confirm Validation',
            text: `Send validation patch request for ${email}?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, Send',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
              disableButton($btn);
              sendPatch(email, status, validation_status, id, $btn, originalText);
            }
        });
    });
}

function sendPatch(email, status = 'pending', validation_status, id = null, $btn, originalText) {
    const data = {
        email: email,
        status: status,
        id: id,
        method: 'patch',
        validation_status: validation_status
    };

    return $.ajax({
        dataType: 'json',
        type: 'POST',
        url: '/admin/fame/buyer/api-patch',
        data: data,
        cache: false,
        beforeSend: function() {
            // Show loading indicator
            // showLoadingIndicator('Processing validation request...');
        },
        success: function(response) {
            // hideLoadingIndicator();
            
            if (response.status === 'success') {
              enableButton($btn, originalText);
                Swal.fire({
                  toast: true,
                  position: 'top-end', // or 'top-start', 'bottom-end', etc.
                  icon: 'success',     // 'success', 'error', 'warning', 'info', or 'question'
                  title: response.message,
                  showConfirmButton: false,
                  timer: 3000,         // Auto-close after 3 seconds
                  timerProgressBar: true,
                  didOpen: (toast) => {
                    toast.addEventListener('mouseenter', Swal.stopTimer)
                    toast.addEventListener('mouseleave', Swal.resumeTimer)
                  }
                });
                // showSuccessMessage(
                //     'Validation Queued',
                //     `${response.message}<br>
                //     <strong>Reference ID:</strong> ${response.data.reference_id}<br>
                //     <strong>Job ID:</strong> ${response.data.job_id}<br>
                //     <strong>Estimated Processing:</strong> ${response.data.estimated_processing_time}`
                // );
                
                // Optionally poll for status updates
                // if (response.data.job_id) {
                //     pollJobStatus(response.data.job_id);
                // }
            } else {
                showErrorMessage('Validation Failed', response.message);
                enableButton($btn, originalText);
            }
        },
        error: function(xhr, status, error) {
            hideLoadingIndicator();
            showErrorMessage('Request Failed', 'An error occurred while processing the validation request.');
            console.error('Validation request error:', error);
            enableButton($btn, originalText);
        }
    });
}





/**
 * Utility functions for UI feedback
 */

function disableButton($btn) {
    $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');
}

function enableButton($btn, originalText) {
    $btn.prop('disabled', false).html(originalText);
}

function showLoadingIndicator(message = 'Processing...') {
    Swal.fire({
        title: 'Processing',
        html: message,
        allowOutsideClick: false,
        didOpen: () => {
          swal.showLoading()
        }
      })
}

function hideLoadingIndicator() {
    Swal.close();
}

function showSuccessMessage(title, message) {
    Swal.fire({
        title: title,
        html: message,
        icon: 'success',
        confirmButtonText: 'OK'
    });
}

function showErrorMessage(title, message) {
    Swal.fire({
        title: title,
        text: message,
        icon: 'error',
        confirmButtonText: 'OK'
    });
}

function showWarningMessage(title, message) {
    Swal.fire({
        title: title,
        text: message,
        icon: 'warning',
        confirmButtonText: 'OK'
    });
}

</script>

<?= $this->endSection(); ?>
