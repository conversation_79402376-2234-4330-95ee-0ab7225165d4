<?php

namespace Modules\FameManagement\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use Modules\FameManagement\Libraries\ValidationQueueProcessor;

/**
 * CLI Command to process Fame Management validation queue
 * 
 * Usage:
 * php spark fame:process-validation-queue
 * php spark fame:process-validation-queue --batch-size=20
 * php spark fame:process-validation-queue --retry-failed
 * php spark fame:process-validation-queue --cleanup --days=30
 */
class ProcessValidationQueue extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Fame Management';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'fame:process-validation-queue';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'Process pending Fame Management validation requests from the queue';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'fame:process-validation-queue [options]';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [
        '--batch-size' => 'Number of jobs to process in this batch (default: 10)',
        '--retry-failed' => 'Retry failed jobs that haven\'t exceeded max attempts',
        '--cleanup' => 'Clean up old completed/failed jobs',
        '--days' => 'Number of days old for cleanup (default: 30)',
        '--stats' => 'Show queue statistics only',
    ];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $processor = new ValidationQueueProcessor();

        // Show statistics if requested
        if (CLI::getOption('stats')) {
            $this->showStats($processor);
            return;
        }

        // Clean up old jobs if requested
        if (CLI::getOption('cleanup')) {
            $days = (int) CLI::getOption('days') ?: 30;
            $this->cleanupOldJobs($processor, $days);
            return;
        }

        // Retry failed jobs if requested
        if (CLI::getOption('retry-failed')) {
            $this->retryFailedJobs($processor);
        }

        // Process pending jobs
        $batchSize = (int) CLI::getOption('batch-size') ?: 10;
        $this->processPendingJobs($processor, $batchSize);
    }

    /**
     * Process pending validation jobs
     */
    private function processPendingJobs(ValidationQueueProcessor $processor, int $batchSize): void
    {
        CLI::write('Processing Fame Management validation queue...', 'yellow');
        CLI::write("Batch size: {$batchSize}", 'light_gray');
        CLI::newLine();

        try {
            $results = $processor->processPendingValidations($batchSize);

            if (isset($results['message'])) {
                CLI::write($results['message'], 'yellow');
                return;
            }

            $successCount = 0;
            $failureCount = 0;

            foreach ($results as $result) {
                if ($result['status'] === 'completed') {
                    CLI::write("✓ Job {$result['job_id']}: {$result['message']}", 'green');
                    $successCount++;
                } else {
                    CLI::write("✗ Job {$result['job_id']}: {$result['message']}", 'red');
                    $failureCount++;
                }
            }

            CLI::newLine();
            CLI::write("Processing complete:", 'white');
            CLI::write("  Successful: {$successCount}", 'green');
            CLI::write("  Failed: {$failureCount}", 'red');

        } catch (\Exception $e) {
            CLI::error('Error processing queue: ' . $e->getMessage());
        }
    }

    /**
     * Retry failed jobs
     */
    private function retryFailedJobs(ValidationQueueProcessor $processor): void
    {
        CLI::write('Retrying failed validation jobs...', 'yellow');

        try {
            $results = $processor->retryFailedJobs();

            if (empty($results)) {
                CLI::write('No failed jobs to retry.', 'yellow');
                return;
            }

            foreach ($results as $result) {
                CLI::write("↻ Job {$result['job_id']} queued for retry (attempt {$result['attempt']})", 'cyan');
            }

            CLI::write("Queued " . count($results) . " jobs for retry.", 'green');

        } catch (\Exception $e) {
            CLI::error('Error retrying failed jobs: ' . $e->getMessage());
        }
    }

    /**
     * Clean up old jobs
     */
    private function cleanupOldJobs(ValidationQueueProcessor $processor, int $days): void
    {
        CLI::write("Cleaning up jobs older than {$days} days...", 'yellow');

        try {
            $deletedCount = $processor->cleanupOldJobs($days);
            CLI::write("Cleaned up {$deletedCount} old jobs.", 'green');

        } catch (\Exception $e) {
            CLI::error('Error cleaning up old jobs: ' . $e->getMessage());
        }
    }

    /**
     * Show queue statistics
     */
    private function showStats(ValidationQueueProcessor $processor): void
    {
        CLI::write('Fame Management Validation Queue Statistics', 'yellow');
        CLI::write(str_repeat('=', 50), 'yellow');

        try {
            $stats = $processor->getQueueStats();

            CLI::write("Total Jobs: {$stats['total']}", 'white');
            CLI::write("Pending: {$stats['pending']}", 'yellow');
            CLI::write("Processing: {$stats['processing']}", 'cyan');
            CLI::write("Completed: {$stats['completed']}", 'green');
            CLI::write("Failed: {$stats['failed']}", 'red');

            if ($stats['total'] > 0) {
                $successRate = round(($stats['completed'] / ($stats['completed'] + $stats['failed'])) * 100, 2);
                CLI::write("Success Rate: {$successRate}%", 'light_gray');
            }

        } catch (\Exception $e) {
            CLI::error('Error getting queue statistics: ' . $e->getMessage());
        }
    }
}
