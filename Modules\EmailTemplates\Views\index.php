<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
<?= $page_title ?>
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item active">Email Templates</li>
<?= $this->endSection() ?>

<?= $this->section('header') ?>
<!-- DataTables CSS is now included in the main layout -->
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<div class="card">
    <div class="card-header">
        <h3 class="card-title">Email Templates</h3>
        <div class="card-tools">
            <a href="<?= base_url('admin/email-templates/create') ?>" class="btn btn-primary btn-sm">
                <i class="fas fa-plus"></i> Create New Template
            </a>
        </div>
    </div>
    <div class="card-body">
        <!-- Filters -->
        <div class="row mb-3">
            <div class="col-md-4">
                <label for="categoryFilter">Filter by Category:</label>
                <select class="form-control" id="categoryFilter">
                    <option value="">All Categories</option>
                    <?php foreach ($categories as $category): ?>
                    <option value="<?= $category ?>"><?= esc($category) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-4">
                <label for="statusFilter">Filter by Status:</label>
                <select class="form-control" id="statusFilter">
                    <option value="">All Status</option>
                    <option value="1" selected>Active</option>
                    <option value="0">Inactive</option>
                </select>
            </div>
        </div>

        <div class="table-responsive">
            <table id="emailTemplatesTable" class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th width="30">#</th>
                        <th width="30"><input type="checkbox" id="selectAll"></th>
                        <th>Name</th>
                        <th>Category</th>
                        <th>Subject</th>
                        <th>Status</th>
                        <th>Updated</th>
                        <th width="120">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via AJAX -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('script') ?>
<!-- DataTables JS -->
<script src="<?= base_url('assets/plugins/datatables/jquery.dataTables.min.js') ?>"></script>
<script src="<?= base_url('assets/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= base_url('assets/plugins/datatables-responsive/js/dataTables.responsive.min.js') ?>"></script>
<script src="<?= base_url('assets/plugins/datatables-responsive/js/responsive.bootstrap4.min.js') ?>"></script>

<script>
let emailTemplatesTable;
let csrfHash = $('meta[name="X-CSRF-TOKEN"]').attr('content');

$(document).ready(function() {
    // Initialize DataTable
    emailTemplatesTable = $('#emailTemplatesTable').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        lengthChange: true,
        autoWidth: false,
        ajax: {
            url: '<?= site_url('admin/email-templates/datatable') ?>',
            type: 'POST',
            data: function(d) {
                d.<?= csrf_token() ?> = csrfHash;
                d.category = $('#categoryFilter').val();
                d.status = $('#statusFilter').val();
            },
            error: function(xhr, error, thrown) {
                console.error('DataTable Error:', error);
                Swal.fire('Error!', 'Failed to load templates. Please refresh the page.', 'error');
            }
        },
        order: [[6, 'desc']], // Order by updated date
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'checkbox', orderable: false, searchable: false },
            { data: 'name' },
            { data: 'category_name' },
            { data: 'subject' },
            { data: 'status_badge', orderable: false, searchable: false },
            { data: 'updated_at' },
            { data: 'actions', orderable: false, searchable: false }
        ],
        drawCallback: function() {
            // Update CSRF token after each draw
            csrfHash = $('meta[name="X-CSRF-TOKEN"]').attr('content');
        }
    });

    // Filter change handlers
    $('#categoryFilter, #statusFilter').on('change', function() {
        emailTemplatesTable.ajax.reload();
    });

    // Duplicate template function
    window.duplicateTemplate = function(templateId) {
        Swal.fire({
            title: 'Duplicate Template?',
            text: 'This will create a copy of the template.',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, duplicate it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `<?= base_url('admin/email-templates/duplicate/') ?>${templateId}`,
                    type: 'POST',
                    data: {
                        <?= csrf_token() ?>: csrfHash
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire('Duplicated!', 'Template has been duplicated.', 'success');
                            emailTemplatesTable.ajax.reload();
                        } else {
                            Swal.fire('Error!', response.message || 'Failed to duplicate template.', 'error');
                        }
                    },
                    error: function() {
                        Swal.fire('Error!', 'Failed to duplicate template.', 'error');
                    }
                });
            }
        });
    };

    // Delete template function
    window.deleteTemplate = function(templateId) {
        Swal.fire({
            title: 'Delete Template?',
            text: 'This action cannot be undone!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `<?= base_url('admin/email-templates/delete/') ?>${templateId}`,
                    type: 'DELETE',
                    data: {
                        <?= csrf_token() ?>: csrfHash
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire('Deleted!', 'Template has been deleted.', 'success');
                            emailTemplatesTable.ajax.reload();
                        } else {
                            Swal.fire('Error!', response.message || 'Failed to delete template.', 'error');
                        }
                    },
                    error: function() {
                        Swal.fire('Error!', 'Failed to delete template.', 'error');
                    }
                });
            }
        });
    };
});
</script>
<?= $this->endSection(); ?>
