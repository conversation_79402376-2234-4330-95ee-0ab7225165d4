<?php

namespace Modules\FameManagement\Models;

use CodeIgniter\Model;

/**
 * Validation Queue Model
 * 
 * Manages the queue for Fame Management validation requests
 * This model handles CRUD operations for the validation_queue table
 */
class ValidationQueueModel extends Model
{
    protected $table = 'fame_validation_queue';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'job_type',
        'payload',
        'status',
        'attempts',
        'max_attempts',
        'last_error',
        'created_at',
        'updated_at',
        'completed_at',
        'scheduled_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'job_type' => 'required|string|max_length[100]',
        'payload' => 'required',
        'status' => 'required|in_list[pending,processing,completed,failed]',
        'attempts' => 'integer',
        'max_attempts' => 'integer'
    ];

    protected $validationMessages = [
        'job_type' => [
            'required' => 'Job type is required',
            'max_length' => 'Job type cannot exceed 100 characters'
        ],
        'payload' => [
            'required' => 'Payload is required'
        ],
        'status' => [
            'required' => 'Status is required',
            'in_list' => 'Status must be one of: pending, processing, completed, failed'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    /**
     * Get pending jobs for processing
     * 
     * @param int $limit Number of jobs to retrieve
     * @return array
     */
    public function getPendingJobs(int $limit = 10): array
    {
        return $this->where('status', 'pending')
                   ->where('attempts <', 'max_attempts', false)
                   ->orderBy('created_at', 'ASC')
                   ->limit($limit)
                   ->findAll();
    }

    /**
     * Get failed jobs that can be retried
     * 
     * @param int $maxAttempts Maximum attempts allowed
     * @return array
     */
    public function getRetryableJobs(int $maxAttempts = 3): array
    {
        return $this->where('status', 'failed')
                   ->where('attempts <', $maxAttempts)
                   ->orderBy('updated_at', 'ASC')
                   ->findAll();
    }

    /**
     * Count jobs by status
     * 
     * @param string $status Job status
     * @return int
     */
    public function countByStatus(string $status): int
    {
        return $this->where('status', $status)->countAllResults();
    }

    /**
     * Increment attempts for a job
     * 
     * @param int $jobId Job ID
     * @return int New attempt count
     */
    public function incrementAttempts(int $jobId): int
    {
        $job = $this->find($jobId);
        if (!$job) {
            return 0;
        }

        $newAttempts = $job['attempts'] + 1;
        $this->update($jobId, ['attempts' => $newAttempts]);
        
        return $newAttempts;
    }

    /**
     * Add a new validation job to the queue
     * 
     * @param array $jobData Job data
     * @return int|bool Job ID on success, false on failure
     */
    public function addValidationJob(array $jobData)
    {
        $data = [
            'job_type' => $jobData['job_type'] ?? 'fame_validation',
            'payload' => is_array($jobData['payload']) ? json_encode($jobData['payload']) : $jobData['payload'],
            'status' => 'pending',
            'attempts' => 0,
            'max_attempts' => $jobData['max_attempts'] ?? 3,
            'scheduled_at' => $jobData['scheduled_at'] ?? date('Y-m-d H:i:s')
        ];

        return $this->insert($data);
    }

    /**
     * Mark job as processing
     * 
     * @param int $jobId Job ID
     * @return bool
     */
    public function markAsProcessing(int $jobId): bool
    {
        return $this->update($jobId, [
            'status' => 'processing',
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Mark job as completed
     * 
     * @param int $jobId Job ID
     * @param array $result Optional result data
     * @return bool
     */
    public function markAsCompleted(int $jobId, array $result = []): bool
    {
        $updateData = [
            'status' => 'completed',
            'completed_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        if (!empty($result)) {
            $updateData['result'] = json_encode($result);
        }

        return $this->update($jobId, $updateData);
    }

    /**
     * Mark job as failed
     * 
     * @param int $jobId Job ID
     * @param string $error Error message
     * @return bool
     */
    public function markAsFailed(int $jobId, string $error): bool
    {
        return $this->update($jobId, [
            'status' => 'failed',
            'last_error' => $error,
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Delete old jobs
     * 
     * @param int $daysOld Number of days old
     * @return int Number of deleted records
     */
    public function deleteOldJobs(int $daysOld = 30): int
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysOld} days"));
        
        return $this->where('created_at <', $cutoffDate)
                   ->whereIn('status', ['completed', 'failed'])
                   ->delete();
    }

    /**
     * Get job statistics for dashboard
     * 
     * @return array
     */
    public function getJobStats(): array
    {
        $stats = [
            'total' => $this->countAll(),
            'pending' => $this->countByStatus('pending'),
            'processing' => $this->countByStatus('processing'),
            'completed' => $this->countByStatus('completed'),
            'failed' => $this->countByStatus('failed')
        ];

        // Calculate success rate
        $total = $stats['completed'] + $stats['failed'];
        $stats['success_rate'] = $total > 0 ? round(($stats['completed'] / $total) * 100, 2) : 0;

        return $stats;
    }

    /**
     * Get recent jobs for monitoring
     * 
     * @param int $limit Number of jobs to retrieve
     * @return array
     */
    public function getRecentJobs(int $limit = 50): array
    {
        return $this->orderBy('created_at', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }
}
