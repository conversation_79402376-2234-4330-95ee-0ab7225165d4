/**
 * Fame Management Validation Sample JavaScript
 * 
 * This file demonstrates how to integrate with the Fame Management
 * validation API using the new guzzlehttp endpoint.
 * 
 * Sample API Key: sk-fame-validation-api-key-12345abcdef67890
 * Sample Endpoint: https://api.example.com/v1
 */

// Sample data structures for validation requests
const sampleValidationData = {
    user_id: 1234,
    email: '<EMAIL>',
    status: 'pending',
    validation_type: 'patch_validation',
    timestamp: new Date().toISOString(),
    metadata: {
        fair_code: 'MFIO2025',
        sector: '02',
        source: 'fame_management',
        ip_address: '***********',
        user_agent: navigator.userAgent
    },
    buyer_data: {
        company_name: 'Sample Company Ltd.',
        contact_person: '<PERSON>',
        phone: '+1234567890',
        country: 'United States',
        industry: 'Manufacturing',
        categories: ['Electronics', 'Automotive', 'Textiles']
    }
};

const samplePatchData = {
    email: '<EMAIL>',
    status: 'approved',
    action: 'patch_update',
    timestamp: new Date().toISOString(),
    updated_fields: {
        verification_status: 'verified',
        approval_date: new Date().toISOString().split('T')[0],
        notes: 'Manual verification completed'
    }
};

/**
 * Send validation patch request (async)
 * This will queue the request for background processing
 */
function sendValidationPatch(email, status = 'pending', id = null) {
    const data = {
        email: email,
        status: status,
        id: id,
        method: 'validatepatch'
    };

    return $.ajax({
        dataType: 'json',
        type: 'POST',
        url: '/admin/fame/buyer/guzzlehttp',
        data: data,
        cache: false,
        beforeSend: function() {
            // Show loading indicator
            showLoadingIndicator('Processing validation request...');
        },
        success: function(response) {
            hideLoadingIndicator();
            
            if (response.status === 'success') {
                showSuccessMessage(
                    'Validation Queued',
                    `${response.message}<br>
                    <strong>Reference ID:</strong> ${response.data.reference_id}<br>
                    <strong>Job ID:</strong> ${response.data.job_id}<br>
                    <strong>Estimated Processing:</strong> ${response.data.estimated_processing_time}`
                );
                
                // Optionally poll for status updates
                if (response.data.job_id) {
                    pollJobStatus(response.data.job_id);
                }
            } else {
                showErrorMessage('Validation Failed', response.message);
            }
        },
        error: function(xhr, status, error) {
            hideLoadingIndicator();
            showErrorMessage('Request Failed', 'An error occurred while processing the validation request.');
            console.error('Validation request error:', error);
        }
    });
}

/**
 * Send regular patch request (synchronous)
 */
function sendPatch(email, status = 'approved') {
    const data = {
        email: email,
        status: status,
        method: 'patch'
    };

    return $.ajax({
        dataType: 'json',
        type: 'POST',
        url: '/admin/fame/buyer/guzzlehttp',
        data: data,
        cache: false,
        beforeSend: function() {
            showLoadingIndicator('Processing patch request...');
        },
        success: function(response) {
            hideLoadingIndicator();
            
            if (response.status === 'success') {
                showSuccessMessage(
                    'Patch Successful',
                    `${response.message}<br>
                    <strong>Email:</strong> ${response.data.email}<br>
                    <strong>Status:</strong> ${response.data.status}<br>
                    <strong>Processed:</strong> ${response.data.processed_at}`
                );
            } else {
                showErrorMessage('Patch Failed', response.message);
            }
        },
        error: function(xhr, status, error) {
            hideLoadingIndicator();
            showErrorMessage('Request Failed', 'An error occurred while processing the patch request.');
            console.error('Patch request error:', error);
        }
    });
}

/**
 * Poll job status for async requests
 */
function pollJobStatus(jobId, maxAttempts = 30, interval = 5000) {
    let attempts = 0;
    
    const poll = setInterval(() => {
        attempts++;
        
        // Check job status via API (you'd need to implement this endpoint)
        $.ajax({
            url: `/admin/fame/buyer/job-status/${jobId}`,
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.status === 'completed') {
                    clearInterval(poll);
                    showSuccessMessage('Validation Complete', 'Your validation request has been processed successfully.');
                } else if (response.status === 'failed') {
                    clearInterval(poll);
                    showErrorMessage('Validation Failed', response.error || 'The validation request failed to process.');
                } else if (attempts >= maxAttempts) {
                    clearInterval(poll);
                    showWarningMessage('Status Check Timeout', 'Unable to get final status. Please check manually.');
                }
                // Continue polling if status is 'pending' or 'processing'
            },
            error: function() {
                if (attempts >= maxAttempts) {
                    clearInterval(poll);
                    showWarningMessage('Status Check Failed', 'Unable to check job status. Please verify manually.');
                }
            }
        });
    }, interval);
}

/**
 * Example usage with DataTable integration
 */
function initializeValidationButtons() {
    // Validation patch button click handler
    $(document).on('click', '.btn-validate-patch', function() {
        const row = $(this).closest('tr');
        const table = $('#profile_table').DataTable();
        const rowData = table.row(row).data();
        
        const email = rowData.email;
        const status = 'pending';
        const id = rowData.id;
        
        // Confirm before sending
        Swal.fire({
            title: 'Confirm Validation',
            text: `Send validation patch request for ${email}?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, Send',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                sendValidationPatch(email, status, id);
            }
        });
    });

    // Regular patch button click handler
    $(document).on('click', '.btn-patch', function() {
        const row = $(this).closest('tr');
        const table = $('#profile_table').DataTable();
        const rowData = table.row(row).data();
        
        const email = rowData.email;
        const status = 'approved';
        
        // Confirm before sending
        Swal.fire({
            title: 'Confirm Patch',
            text: `Send patch request for ${email}?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, Send',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                sendPatch(email, status);
            }
        });
    });
}

/**
 * Utility functions for UI feedback
 */
function showLoadingIndicator(message = 'Processing...') {
    // Implementation depends on your UI framework
    console.log('Loading:', message);
}

function hideLoadingIndicator() {
    // Implementation depends on your UI framework
    console.log('Loading complete');
}

function showSuccessMessage(title, message) {
    Swal.fire({
        title: title,
        html: message,
        icon: 'success',
        confirmButtonText: 'OK'
    });
}

function showErrorMessage(title, message) {
    Swal.fire({
        title: title,
        text: message,
        icon: 'error',
        confirmButtonText: 'OK'
    });
}

function showWarningMessage(title, message) {
    Swal.fire({
        title: title,
        text: message,
        icon: 'warning',
        confirmButtonText: 'OK'
    });
}

// Initialize when document is ready
$(document).ready(function() {
    initializeValidationButtons();
    
    // Log sample data for reference
    console.log('Sample Validation Data:', sampleValidationData);
    console.log('Sample Patch Data:', samplePatchData);
});
