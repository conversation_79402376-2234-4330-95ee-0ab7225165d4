<?php

namespace Modules\FameManagement\Controllers;

use App\Controllers\AdminController;
use CodeIgniter\HTTP\ResponseInterface;

use \Modules\FameManagement\Models\BuyerModel;
use \Modules\FameManagement\Models\ExhibitorModel;
use \Modules\CitemDbManagement\Models\VpsModel;
use \Modules\FameManagement\Models\GuzzleModel;

use \Modules\FameManagement\Config\FameManagement as fame_config;

use \Modules\GuzzleHttp\Services\GuzzleService;

use \Hermawan\DataTables\DataTable;

class BuyerController extends AdminController
{

    protected $buyer_model;
    protected $exhibitor_model;
    protected $guzzleModel;

    public function __construct()
    {
        parent::__construct();

        $this->buyer_model = new BuyerModel();
        $this->exhibitor_model = new ExhibitorModel();
        $this->guzzleModel = new GuzzleModel();
    }


    public function index()
    {
        $config = new fame_config();
        $filter = [
            'a.status' => 1,
            'account_type' => 1, //trade buyer
            'a.mf_event' => 'yes', //joining the event for the year
        ];  

        $fameplus = $this->buyer_model->getUsers($filter);
        //get count
        $fameplus_count = count($fameplus);
        $vps_model = new VpsModel();
        $citem_count = $vps_model->getFamePreRegCount($config->fair_code);
        // $approved_count = 0;
        // $pending_count = 0;
        // $disapproved_count = 0;
        // foreach ($citem_count['per_status'] as $row) {
        //     if ($row['validation_status'] == 'Approved Trade') {
        //         $approved_count = $row['total'];
        //     } elseif ($row['validation_status'] == 'Pending Trade') {
        //         $pending_count = $row['total'];
        //     } elseif ($row['validation_status'] == 'Disapproved Trade') {
        //         $disapproved_count = $row['total'];
        //     }
        // }
        //get fplus_email from $fameplus
        $emails = array_column($fameplus, 'fplus_email');

        $filter2 = [
            // 'b.validation_status' => 'Approved Trade', //visitor
            // 'b.visitor_type' => 'Trade Buyer', //joining the event for the year
            'b.sector' => '02',
        ];  
        $whereFilter = [
            // 'b.fair_code' => 'MFIO',
            'b.fair_code' => 'MFIO2025',
        ];

        $inFilter = [
            'a.email' => $emails,
        ];

        $v_citem_model = new VpsModel();
        $citem_vps = $v_citem_model->getVpsList($filter2,$whereFilter,$inFilter);

        foreach ($fameplus as $contact) {
            $found = false;
            foreach ($citem_vps as $vps) {
                if ($contact['fplus_email'] == $vps['citem_email']) {
                    $combinedData[] = array_merge($vps, $contact);
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                $contact['citemdb_status'] = '';
                $combinedData[] = $contact;
            }
        }

        // dd($combinedData);

        $data = [
            'title' => 'FamePlus VPS',
            'page_title' => 'FamePlus VPS List',
            'data' => $combinedData,
            'fameplus_count' => $fameplus_count,
            'status_count' => $citem_count['per_status'],
            'citem_total' => $citem_count['overall_total'],
        ];

        return view('Modules\FameManagement\Views\index', $data);
    }

    public function apiVisitorData($id){
        // $id = '9787';
        // $id = $this->request->getGet('id');

        $category = $this->buyer_model->getCategory();

        $filter = [];

        $data = $this->buyer_model->getApiPayloadData($id,$filter);

        foreach ($data as $key => $value) {
            $cat_ids = explode(',', $value['category']);
            $cat_names = [];
            foreach ($cat_ids as $cat_id) {
                foreach ($category as $key2 => $value2) {
                    if ($value2['id'] == $cat_id) {
                        $cat_names[] = $value2['name'];
                        break;
                    }
                }
            }
            $data[$key]['prod_major_category'] = implode('|', $cat_names);
        }
        unset($data[0]['category']);

        //remove all null and blank values from array
        foreach ($data[0] as $key => $value) {
            if (is_null($value) || $value == '') {
                unset($data[0][$key]);
            }
        }
        return $data?$data[0]:[];

        // return $this->response->setStatusCode(403);
    }

    public function apiPost(){
        if(!$this->request->isAJAX()){
            return $this->response->setStatusCode(403)->setJSON;
            [
                'status' => 'error',
                'message' => 'Only AJAX request are allowed'
            ];
        }

        $config = new fame_config();

        try{
            $id = $this->request->getPost('id');
            $method = $this->request->getPost('method');
            $email = $this->request->getPost('email');
            $payload = $this->apiVisitorData($id);
            // $payload['fair_code'] = $config->fair_code;
            unset($payload['account_type']);
            unset($payload['status']);
            unset($payload['validation_status']);

            // print_r(json_encode($payload));exit();

            if(empty($method) || empty($email) || empty($payload)){
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Method, email and payload are required'
                ]);
            }

            switch($method){
                case 'post':
                    return $this->handlePost($email, $payload);
                default:
                    return $this->response->setJSON([
                        'status' => 'error',
                        'message' => 'Invalid method'
                    ]);
            }


        }catch(\Exception $e){
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'An error occured while processing the request.'.$e->getMessage()
            ]);
        }

    }

    private function handlePost($email, $payload)
    {
        try{
            $result = $this->guzzleModel->guz_post($payload);
            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Request sent successfully.'
            ]);

        }catch(\Exception $e){
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'An error occured while processing the request.'.$e->getMessage()
            ]);
        }
    }


    public function apiPatch(){
        

        if(!$this->request->isAJAX()){
            return $this->response->setStatusCode(403)->setJSON;
            [
                'status' => 'error',
                'message' => 'Only AJAX request are allowed'
            ];
        }


        try{
            $method = $this->request->getPost('method');
            $email = $this->request->getPost('email');
            $status = $this->request->getPost('status');
            $validation_status = $this->request->getPost('validation_status');
            $id = $this->request->getPost('id');

            if(empty($method) || empty($email)){
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Method and email are required'
                ]);
            }

            switch($method){
                case 'patch':
                    return $this->handlePatch($email, $validation_status);
                case 'validatepatch':
                    return $this->handleBuyerPatch($email, $validation_status);
                default:
                    return $this->response->setJSON([
                        'status' => 'error',
                        'message' => 'Invalid method'
                    ]);
            }


        }catch(\Exception $e){
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'An error occured while processing the request.'.$e->getMessage()
            ]);
        }
    }


    private function handlePatch($email, $validation_status)
    {
        // $uri = 'validate_buyer/email/'.$email;
        $data = [
            'validation_status' => $validation_status
        ];

        try{
            $result = $this->guzzleModel->guz_patch($data,$email);
            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Request sent successfully.'
            ]);

        }catch(\Exception $e){
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'An error occured while processing the request.'.$e->getMessage()
            ]);
        }
    }









}
