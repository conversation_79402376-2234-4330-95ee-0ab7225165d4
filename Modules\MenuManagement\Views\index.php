<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('page_title') ?>
<?= $page_title ?>
<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item active">Menu Management</li>
<?= $this->endSection() ?>

<?= $this->section('header') ?>
<!-- DataTables CSS is now included in the main layout -->
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<?php if (hasPermission('menu.dashboard')) : ?>
    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3><?= $stats['total'] ?></h3>
                    <p>Total Menus</p>
                </div>
                <div class="icon">
                    <i class="fas fa-list"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3><?= $stats['active'] ?></h3>
                    <p>Active Menus</p>
                </div>
                <div class="icon">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3><?= $stats['inactive'] ?></h3>
                    <p>Inactive Menus</p>
                </div>
                <div class="icon">
                    <i class="fas fa-times-circle"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-primary">
                <div class="inner">
                    <h3><?= $stats['root_menus'] ?></h3>
                    <p>Root Menus</p>
                </div>
                <div class="icon">
                    <i class="fas fa-sitemap"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Cache Status Card -->
    <div class="row">
        <div class="col-md-12">
            <div class="card card-outline card-info collapsed-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-database"></i> Cache Management
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body" style="display: none;">
                    <div class="row">
                        <div class="col-md-8">
                            <div id="cache-info">
                                <p class="text-muted">Click "Refresh Status" to view current cache information.</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="btn-group-vertical w-100">
                                <button type="button" class="btn btn-info btn-sm mb-2" id="refresh-cache-status">
                                    <i class="fas fa-sync"></i> Refresh Status
                                </button>
                                <button type="button" class="btn btn-secondary btn-sm mb-2" id="warmup-cache-btn">
                                    <i class="fas fa-fire"></i> Warmup Cache
                                </button>
                                <button type="button" class="btn btn-warning btn-sm" id="clear-cache-btn">
                                    <i class="fas fa-broom"></i> Clear All Cache
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-header">
        <h3 class="card-title">Menu Management</h3>
        <div class="card-tools">
            <?php if (hasPermission('menu.manage')) : ?>
                <a href="<?= route_to('menu.create') ?>" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus"></i> Add New Menu
                </a>
            <?php endif; ?>
        </div>
    </div>
    <div class="card-body">
        <!-- Flash Messages -->
        <?php if (session()->getFlashdata('success')) : ?>
            <div class="alert alert-success alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <i class="icon fas fa-check"></i> <?= session()->getFlashdata('success') ?>
            </div>
        <?php endif; ?>
        <?php if (session()->getFlashdata('error')) : ?>
            <div class="alert alert-danger alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <i class="icon fas fa-ban"></i> <?= session()->getFlashdata('error') ?>
            </div>
        <?php endif; ?>

        <!-- Bulk Actions -->
        <?php if (hasPermission('menu.manage')) : ?>
        <div class="row mb-3">
            <div class="col-md-8">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-success btn-sm" id="bulk-activate" disabled>
                        <i class="fas fa-check"></i> Activate Selected
                    </button>
                    <button type="button" class="btn btn-warning btn-sm" id="bulk-deactivate" disabled>
                        <i class="fas fa-times"></i> Deactivate Selected
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" id="bulk-delete" disabled>
                        <i class="fas fa-trash"></i> Delete Selected
                    </button>
                </div>
                <span class="ml-2 text-muted" id="selected-count">0 items selected</span>
            </div>
            <div class="col-md-4 text-right">
                <!-- Cache Management -->
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-info btn-sm" id="cache-status" title="View Cache Status">
                        <i class="fas fa-info-circle"></i> Cache Status
                    </button>
                    <button type="button" class="btn btn-secondary btn-sm" id="warmup-cache" title="Pre-generate cache for all roles">
                        <i class="fas fa-fire"></i> Warmup
                    </button>
                    <button type="button" class="btn btn-warning btn-sm" id="clear-cache" title="Clear all menu caches">
                        <i class="fas fa-broom"></i> Clear Cache
                    </button>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- DataTable -->
        <div class="table-responsive">
            <table id="menuTable" class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th width="30">#</th>
                        <?php if (hasPermission('menu.manage')) : ?>
                        <th style="width: 30px">
                            <input type="checkbox" id="select-all">
                        </th>
                        <?php endif; ?>
                        <th>Menu Label</th>
                        <th>URL</th>
                        <th>Roles</th>
                        <!-- <th>Legacy Permission</th> -->
                        <th>Parent</th>
                        <th>Status</th>
                        <th style="width: 100px">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via AJAX -->
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer">
        <div class="float-right">
            <!-- <small>Page rendered in <strong>{elapsed_time}</strong> seconds.</small> -->
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('script') ?>
<!-- DataTables scripts are now included in the main layout -->
<!-- Additional DataTables plugins -->
<script src="<?= base_url("assets/plugins/jszip/jszip.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/pdfmake/pdfmake.min.js") ?>"></script>
<script src="<?= base_url("assets/plugins/pdfmake/vfs_fonts.js") ?>"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.colVis.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = $('#menuTable').DataTable({
        processing: true,
        serverSide: true,
        pageLength: 25,
        responsive: true,
        language: {
            processing: '<i class="fas fa-spinner fa-spin"></i> Loading...'
        },
        ajax: {
            url: '<?= site_url('admin/menu/datatable'); ?>',
            type: 'POST'
        },
        order: [['<?= hasPermission('menu.manage') ? 2 : 1 ?>', 'asc']],
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false }
            <?php if (hasPermission('menu.manage')) : ?>
            ,{ data: 'checkbox', orderable: false, searchable: false }
            <?php endif; ?>
            ,{ data: 'label', }
            ,{ data: 'url',}
            ,{ data: 'roles', orderable: false }
            // ,{ data: 'permission_name', }
            ,{ data: 'parent_label', }
            ,{ data: 'active', }
            ,{ data: 'actions', orderable: false, searchable: false }
        ],
        
        
    });

    // Select all checkbox
    $('#select-all').on('click', function() {
        var checked = this.checked;
        $('.select-item').prop('checked', checked);
        updateBulkButtons();
    });

    // Individual checkbox
    $(document).on('change', '.select-item', function() {
        updateBulkButtons();

        // Update select all checkbox
        var totalItems = $('.select-item').length;
        var checkedItems = $('.select-item:checked').length;
        $('#select-all').prop('indeterminate', checkedItems > 0 && checkedItems < totalItems);
        $('#select-all').prop('checked', checkedItems === totalItems);
    });

    // Update bulk action buttons
    function updateBulkButtons() {
        var selectedItems = $('.select-item:checked').length;
        var bulkButtons = $('#bulk-activate, #bulk-deactivate, #bulk-delete');

        if (selectedItems > 0) {
            bulkButtons.prop('disabled', false);
            $('#selected-count').text(selectedItems + ' item' + (selectedItems > 1 ? 's' : '') + ' selected');
        } else {
            bulkButtons.prop('disabled', true);
            $('#selected-count').text('0 items selected');
        }
    }

    // Bulk activate
    $('#bulk-activate').on('click', function() {
        var selectedIds = $('.select-item:checked').map(function() {
            return $(this).val();
        }).get();

        if (selectedIds.length === 0) {
            Swal.fire('Warning', 'Please select items to activate', 'warning');
            return;
        }

        Swal.fire({
            title: 'Activate Menus',
            text: 'Are you sure you want to activate ' + selectedIds.length + ' selected menu(s)?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, activate!'
        }).then((result) => {
            if (result.isConfirmed) {
                performBulkAction('<?= site_url('admin/menu/bulk-activate') ?>', selectedIds, 'activate');
            }
        });
    });

    // Bulk deactivate
    $('#bulk-deactivate').on('click', function() {
        var selectedIds = $('.select-item:checked').map(function() {
            return $(this).val();
        }).get();

        if (selectedIds.length === 0) {
            Swal.fire('Warning', 'Please select items to deactivate', 'warning');
            return;
        }

        Swal.fire({
            title: 'Deactivate Menus',
            text: 'Are you sure you want to deactivate ' + selectedIds.length + ' selected menu(s)?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#ffc107',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, deactivate!'
        }).then((result) => {
            if (result.isConfirmed) {
                performBulkAction('<?= site_url('admin/menu/bulk-deactivate') ?>', selectedIds, 'deactivate');
            }
        });
    });

    // Bulk delete
    $('#bulk-delete').on('click', function() {
        var selectedIds = $('.select-item:checked').map(function() {
            return $(this).val();
        }).get();

        if (selectedIds.length === 0) {
            Swal.fire('Warning', 'Please select items to delete', 'warning');
            return;
        }

        Swal.fire({
            title: 'Delete Menus',
            text: 'Are you sure you want to delete ' + selectedIds.length + ' selected menu(s)? This action cannot be undone!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, delete!'
        }).then((result) => {
            if (result.isConfirmed) {
                performBulkAction('<?= site_url('admin/menu/bulk-delete') ?>', selectedIds, 'delete');
            }
        });
    });

    // Individual delete
    $(document).on('click', '.btn-delete', function() {
        var menuId = $(this).data('id');

        Swal.fire({
            title: 'Delete Menu',
            text: 'Are you sure you want to delete this menu? This action cannot be undone!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, delete!'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = '<?= site_url('admin/menu/delete/') ?>/' + menuId;
            }
        });
    });

    // Perform bulk action
    function performBulkAction(url, ids, action) {
        $.ajax({
            url: url,
            type: 'POST',
            data: {
                ids: ids,
                <?= csrf_token() ?>: '<?= csrf_hash() ?>'
            },
            beforeSend: function() {
                Swal.fire({
                    title: 'Processing...',
                    text: 'Please wait while we ' + action + ' the selected items.',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });
            },
            success: function(response) {
                if (response.success) {
                    Swal.fire('Success!', response.message, 'success');
                    table.ajax.reload();
                    $('#select-all').prop('checked', false);
                    updateBulkButtons();
                } else {
                    Swal.fire('Error!', response.message, 'error');
                }
            },
            error: function() {
                Swal.fire('Error!', 'An error occurred while processing your request.', 'error');
            }
        });
    }

    // Cache Management Functions
    function refreshCacheStatus() {
        $.get('<?= route_to('menu.cache_status') ?>')
            .done(function(response) {
                let html = '<div class="row">';
                html += '<div class="col-md-6">';
                html += '<h6><i class="fas fa-info-circle"></i> Cache Statistics</h6>';
                html += '<table class="table table-sm table-borderless">';
                html += '<tr><td><strong>Total Cache Files:</strong></td><td>' + response.cache_status.total_cache_files + '</td></tr>';
                html += '<tr><td><strong>Menu Cache Files:</strong></td><td>' + response.cache_status.menu_cache_files + '</td></tr>';
                html += '<tr><td><strong>Cache Directory:</strong></td><td><code>' + response.cache_status.cache_directory + '</code></td></tr>';
                html += '<tr><td><strong>Last Updated:</strong></td><td>' + response.last_updated + '</td></tr>';
                html += '</table></div>';

                html += '<div class="col-md-6">';
                if (response.cache_files.length > 0) {
                    html += '<h6><i class="fas fa-file"></i> Recent Cache Files</h6>';
                    html += '<div style="max-height: 200px; overflow-y: auto;">';
                    response.cache_files.slice(0, 10).forEach(function(file) {
                        let badge = file.is_menu_cache ? '<span class="badge badge-primary">Menu</span>' : '<span class="badge badge-secondary">Other</span>';
                        html += '<div class="d-flex justify-content-between align-items-center border-bottom py-1">';
                        html += '<div><small><strong>' + file.name + '</strong> ' + badge + '</small></div>';
                        html += '<div><small class="text-muted">' + (file.size / 1024).toFixed(1) + 'KB</small></div>';
                        html += '</div>';
                    });
                    html += '</div>';
                } else {
                    html += '<div class="alert alert-info">No cache files found</div>';
                }
                html += '</div></div>';

                $('#cache-info').html(html);
            })
            .fail(function() {
                $('#cache-info').html('<div class="alert alert-danger">Failed to load cache status</div>');
            });
    }

    $('#refresh-cache-status').click(refreshCacheStatus);

    $('#clear-cache-btn').click(function() {
        Swal.fire({
            title: 'Clear All Menu Cache?',
            text: 'This will clear all menu caches and force regeneration on next access.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, clear cache!',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                $.post('<?= route_to('menu.clear_cache') ?>', {
                    <?= csrf_token() ?>: '<?= csrf_hash() ?>'
                })
                .done(function(response) {
                    if (response.success) {
                        Swal.fire('Success!', response.message, 'success');
                        refreshCacheStatus(); // Refresh the status display
                    } else {
                        Swal.fire('Error!', response.message, 'error');
                    }
                })
                .fail(function() {
                    Swal.fire('Error!', 'Failed to clear cache.', 'error');
                });
            }
        });
    });

    $('#warmup-cache-btn').click(function() {
        Swal.fire({
            title: 'Warming up cache...',
            text: 'Pre-generating menu cache for all active roles.',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        $.post('<?= route_to('menu.warmup_cache') ?>', {
            <?= csrf_token() ?>: '<?= csrf_hash() ?>'
        })
        .done(function(response) {
            if (response.success) {
                Swal.fire({
                    title: 'Cache Warmed Up!',
                    html: '<div class="text-left">' +
                          '<strong>Roles Processed:</strong> ' + response.roles_processed + '<br>' +
                          '<strong>Cache Entries Created:</strong> ' + response.caches_created + '<br>' +
                          '<strong>Duration:</strong> ' + response.duration_ms + 'ms' +
                          '</div>',
                    icon: 'success'
                }).then(() => {
                    refreshCacheStatus(); // Refresh the status display
                });
            } else {
                Swal.fire('Error!', response.message || 'Failed to warm up cache.', 'error');
            }
        })
        .fail(function() {
            Swal.fire('Error!', 'Failed to warm up cache.', 'error');
        });
    });

    // Auto-hide alerts
    window.setTimeout(function() {
        $('.alert').fadeTo(500, 0).slideUp(500, function() {
            $(this).remove();
        });
    }, 5000);
});
</script>
<?= $this->endSection() ?>