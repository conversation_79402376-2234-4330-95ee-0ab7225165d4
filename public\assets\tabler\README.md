# Tabler Assets

This directory contains Tabler framework assets for the UI migration from AdminLTE3.

## Structure

- `css/` - Tabler CSS files
- `js/` - Tabler JavaScript files  
- `icons/` - Tabler Icons
- `img/` - Tabler images and logos

## Version

Tabler v1.0.0-beta20 (Latest stable)

## CDN Links Used

For faster implementation, we're using CDN links initially:

### CSS
- https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta17/dist/css/tabler.min.css
- https://cdn.jsdelivr.net/npm/@tabler/icons@latest/icons-sprite.svg

### JS  
- https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta17/dist/js/tabler.min.js

## Local Assets

You can download and host locally by:

1. Download from: https://github.com/tabler/tabler/releases
2. Extract to this directory
3. Update references in layout files

## Migration Notes

- Tabler uses Bootstrap 5 (vs AdminLTE3's Bootstrap 4)
- Icon system changed from FontAwesome to Tabler Icons
- Layout structure is different but similar concepts
