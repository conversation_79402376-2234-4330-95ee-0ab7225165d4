/* Tabler Custom CSS for CITEM System */

/* Brand customizations */
.navbar-brand-text {
    font-weight: 600;
    font-size: 1.1rem;
    margin-left: 0.5rem;
}

/* Sidebar customizations */
.navbar-vertical {
    width: 15rem;
}

.navbar-vertical .navbar-brand {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.navbar-vertical .navbar-brand-image {
    border-radius: 50%;
    width: 32px;
    height: 32px;
    object-fit: cover;
}

/* Navigation improvements */
.nav-link-icon {
    margin-right: 0.5rem;
    opacity: 0.7;
}

.nav-link.active .nav-link-icon {
    opacity: 1;
}

/* Card enhancements */
.card {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    font-weight: 600;
}

/* DataTables customizations */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_processing,
.dataTables_wrapper .dataTables_paginate {
    color: inherit;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0.375rem 0.75rem;
    margin-left: 0.125rem;
    border: 1px solid transparent;
    border-radius: 0.25rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: var(--tblr-primary);
    color: white !important;
    border-color: var(--tblr-primary);
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: var(--tblr-gray-100);
    border-color: var(--tblr-gray-300);
    color: var(--tblr-dark) !important;
}

/* Form improvements */
.form-control:focus {
    border-color: var(--tblr-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--tblr-primary-rgb), 0.25);
}

.btn-primary {
    background-color: var(--tblr-primary);
    border-color: var(--tblr-primary);
}

.btn-primary:hover {
    background-color: var(--tblr-primary);
    border-color: var(--tblr-primary);
    opacity: 0.9;
}

/* Alert improvements */
.alert {
    border: none;
    border-radius: 0.5rem;
}

.alert-success {
    background-color: rgba(var(--tblr-success-rgb), 0.1);
    color: var(--tblr-success);
}

.alert-danger {
    background-color: rgba(var(--tblr-danger-rgb), 0.1);
    color: var(--tblr-danger);
}

.alert-warning {
    background-color: rgba(var(--tblr-warning-rgb), 0.1);
    color: var(--tblr-warning);
}

.alert-info {
    background-color: rgba(var(--tblr-info-rgb), 0.1);
    color: var(--tblr-info);
}

/* Page header improvements */
.page-header {
    background: white;
    border-bottom: 1px solid var(--tblr-border-color);
    margin-bottom: 1.5rem;
}

.page-title {
    font-weight: 600;
    color: var(--tblr-dark);
}

.page-pretitle {
    color: var(--tblr-muted);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 600;
}

/* Breadcrumb improvements */
.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0.5rem 0 0 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: var(--tblr-muted);
}

/* Table improvements */
.table {
    --bs-table-bg: transparent;
}

.table-striped > tbody > tr:nth-of-type(odd) > td,
.table-striped > tbody > tr:nth-of-type(odd) > th {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Button improvements */
.btn {
    font-weight: 500;
    border-radius: 0.375rem;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Badge improvements */
.badge {
    font-weight: 500;
    border-radius: 0.375rem;
}

/* Modal improvements */
.modal-header {
    border-bottom: 1px solid var(--tblr-border-color);
    background-color: var(--tblr-bg-surface);
}

.modal-footer {
    border-top: 1px solid var(--tblr-border-color);
    background-color: var(--tblr-bg-surface);
}

/* Dropdown improvements */
.dropdown-menu {
    border: 1px solid var(--tblr-border-color);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.dropdown-item:hover,
.dropdown-item:focus {
    background-color: var(--tblr-gray-50);
}

/* Avatar improvements */
.avatar {
    border: 2px solid rgba(255, 255, 255, 0.2);
}

/* Responsive improvements */
@media (max-width: 768px) {
    .navbar-brand-text {
        display: none;
    }
    
    .page-header {
        padding: 1rem 0;
    }
    
    .page-title {
        font-size: 1.5rem;
    }
}

/* Dark mode adjustments */
[data-bs-theme="dark"] .card {
    background-color: var(--tblr-dark);
    border-color: rgba(255, 255, 255, 0.1);
}

[data-bs-theme="dark"] .page-header {
    background-color: var(--tblr-dark);
    border-color: rgba(255, 255, 255, 0.1);
}

[data-bs-theme="dark"] .table-striped > tbody > tr:nth-of-type(odd) > td,
[data-bs-theme="dark"] .table-striped > tbody > tr:nth-of-type(odd) > th {
    background-color: rgba(255, 255, 255, 0.02);
}

/* Loading spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom utilities */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.shadow-sm {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}

.border-start-primary {
    border-left: 3px solid var(--tblr-primary) !important;
}

.border-start-success {
    border-left: 3px solid var(--tblr-success) !important;
}

.border-start-warning {
    border-left: 3px solid var(--tblr-warning) !important;
}

.border-start-danger {
    border-left: 3px solid var(--tblr-danger) !important;
}
