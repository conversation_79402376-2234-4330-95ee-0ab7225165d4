# Fame Management Validation API Integration

This document describes the complete implementation of the Fame Management validation system with async processing capabilities using GuzzleHTTP.

## Overview

The system provides:
- **Async validation processing** via database queue
- **Sample data structures** for API integration
- **GuzzleHTTP integration** with configurable endpoints
- **Background job processing** with retry logic
- **<PERSON><PERSON><PERSON> commands** for queue management
- **Frontend JavaScript integration** examples

## Components Created

### 1. Configuration Files

#### `Modules/FameManagement/Config/FameValidationApi.php`
- API endpoint configuration
- Sample payload structures
- Authentication settings
- Queue configuration

**Sample API Key**: `sk-fame-validation-api-key-12345abcdef67890`
**Sample Endpoint**: `https://api.example.com/v1`

### 2. Controller Integration

#### `Modules/FameManagement/Controllers/BuyerController.php`
- Added `guzzlehttp()` method
- Handles both sync and async validation requests
- Supports two methods:
  - `validatepatch` - Async processing via queue
  - `patch` - Synchronous processing

### 3. Queue System

#### `Modules/FameManagement/Models/ValidationQueueModel.php`
- Database model for queue management
- CRUD operations for validation jobs
- Status tracking and retry logic

#### `Modules/FameManagement/Libraries/ValidationQueueProcessor.php`
- Background job processor
- Batch processing capabilities
- Error handling and retry logic

### 4. Database Migration

#### `Modules/FameManagement/Database/Migrations/2025-01-01-120000_CreateFameValidationQueueTable.php`
- Creates `fame_validation_queue` table
- Includes sample data for testing
- Proper indexing for performance

### 5. CLI Command

#### `Modules/FameManagement/Commands/ProcessValidationQueue.php`
- Process pending validation jobs
- Retry failed jobs
- Clean up old jobs
- Show queue statistics

### 6. Frontend Integration

#### `Modules/FameManagement/Views/assets/js/validation-sample.js`
- JavaScript functions for API calls
- Sample data structures
- DataTable integration examples
- SweetAlert2 integration for user feedback

## Sample Data Structures

### Validation Payload
```json
{
    "user_id": 1234,
    "email": "<EMAIL>",
    "status": "pending",
    "validation_type": "patch_validation",
    "timestamp": "2025-01-01 12:00:00",
    "metadata": {
        "fair_code": "MFIO2025",
        "sector": "02",
        "source": "fame_management",
        "ip_address": "***********",
        "user_agent": "Mozilla/5.0..."
    },
    "buyer_data": {
        "company_name": "Sample Company Ltd.",
        "contact_person": "John Doe",
        "phone": "+1234567890",
        "country": "United States",
        "industry": "Manufacturing",
        "categories": ["Electronics", "Automotive", "Textiles"]
    }
}
```

### Patch Payload
```json
{
    "email": "<EMAIL>",
    "status": "approved",
    "action": "patch_update",
    "timestamp": "2025-01-01 12:00:00",
    "updated_fields": {
        "verification_status": "verified",
        "approval_date": "2025-01-01",
        "notes": "Manual verification completed"
    }
}
```

## Usage Examples

### Frontend JavaScript
```javascript
// Async validation (queued)
sendValidationPatch('<EMAIL>', 'pending', 1234);

// Sync patch
sendPatch('<EMAIL>', 'approved');
```

### AJAX Requests
```javascript
$.ajax({
    dataType: 'json',
    type: 'POST',
    url: '/admin/fame/buyer/guzzlehttp',
    data: {
        email: '<EMAIL>',
        status: 'pending',
        id: 1234,
        method: 'validatepatch'
    },
    success: function(response) {
        console.log('Job queued:', response.data.job_id);
    }
});
```

### CLI Commands
```bash
# Process pending validation jobs
php spark fame:process-validation-queue

# Process with custom batch size
php spark fame:process-validation-queue --batch-size=20

# Retry failed jobs
php spark fame:process-validation-queue --retry-failed

# Show queue statistics
php spark fame:process-validation-queue --stats

# Clean up old jobs
php spark fame:process-validation-queue --cleanup --days=30
```

## Setup Instructions

### 1. Run Database Migration
```bash
php spark migrate -n "Modules\FameManagement"
```

### 2. Configure API Endpoint
Update `Modules/GuzzleHttp/Config/GuzzleHttp.php`:
```php
'fame_validation' => [
    'base_url' => 'https://your-actual-api.com/v1',
    'timeout' => 30,
    'headers' => [
        'Accept' => 'application/json',
        'Content-Type' => 'application/json',
        'Authorization' => 'Bearer your-actual-api-key'
    ]
]
```

### 3. Set Up Cron Job (Optional)
Add to your crontab for automatic queue processing:
```bash
# Process validation queue every 5 minutes
*/5 * * * * cd /path/to/your/app && php spark fame:process-validation-queue
```

## API Endpoints

### POST `/admin/fame/buyer/guzzlehttp`

#### Parameters:
- `method` (required): `validatepatch` or `patch`
- `email` (required): User email address
- `status` (optional): Validation status
- `id` (optional): User ID

#### Response Format:
```json
{
    "status": "success|error",
    "message": "Description of result",
    "data": {
        "job_id": 123,
        "reference_id": "VAL_ABC123",
        "queued_at": "2025-01-01 12:00:00"
    }
}
```

## Queue Table Schema

The `fame_validation_queue` table includes:
- `id` - Primary key
- `job_type` - Type of validation job
- `payload` - JSON payload data
- `status` - pending, processing, completed, failed
- `attempts` - Number of processing attempts
- `max_attempts` - Maximum retry attempts
- `last_error` - Last error message
- `result` - Processing result data
- `created_at`, `updated_at`, `completed_at`, `scheduled_at` - Timestamps

## Error Handling

The system includes comprehensive error handling:
- Database transaction rollbacks
- Retry logic for failed jobs
- Detailed error logging
- User-friendly error messages
- Graceful degradation

## Security Considerations

- API keys should be stored in environment variables
- Input validation on all endpoints
- Rate limiting should be implemented
- SSL/TLS encryption for API calls
- Proper authentication and authorization

## Monitoring and Maintenance

- Use CLI commands to monitor queue status
- Set up alerts for failed jobs
- Regular cleanup of old completed jobs
- Monitor API response times and success rates
- Log analysis for troubleshooting
