/* Custom SMS System Styles - Tabler Compatible */
/* Font Awesome is now included in the main layout */

.button-row {
    display: flex;
    flex-wrap: wrap; /* Allow buttons to wrap to next line on smaller screens */
    justify-content: space-around;
    gap: 1rem; /* Use gap instead of margins for better spacing */
}

.custom-button {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 1.25rem;
    font-size: 1rem;
    background-color: var(--tblr-bg-surface, #f8f9fa);
    border: 1px solid var(--tblr-border-color, #dee2e6);
    cursor: pointer;
    color: var(--tblr-body-color, #1e293b);
    min-width: 250px;
    border-radius: var(--tblr-border-radius, 0.375rem);
    box-shadow: var(--tblr-box-shadow-sm, 0 1px 2px 0 rgba(0, 0, 0, 0.05));
    transition: all 0.2s ease-in-out;
    height: auto;
    min-height: 60px;
    flex-basis: 100%; /* Make buttons full width on small screens */
    text-decoration: none;
    font-weight: 500;
}

/* Media query for larger screens */
@media (min-width: 768px) {
    .custom-button {
        flex-basis: auto; /* Reset width for larger screens */
        max-width: 300px;
    }
}

.custom-button:hover {
    background-color: var(--tblr-gray-50, #f1f5f9);
    border-color: var(--tblr-primary, #206bc4);
    box-shadow: var(--tblr-box-shadow, 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06));
    transform: translateY(-2px); /* Add subtle lift on hover */
    color: var(--tblr-primary, #206bc4);
    text-decoration: none;
}

.custom-button:active {
    background-color: var(--tblr-gray-100, #e2e8f0);
    transform: translateY(1px); /* Add slight press effect */
    box-shadow: var(--tblr-box-shadow-sm, 0 1px 2px 0 rgba(0, 0, 0, 0.05));
}

.custom-button:focus {
    outline: 2px solid var(--tblr-primary, #206bc4);
    outline-offset: 2px;
}

/* Dark mode support */
[data-bs-theme="dark"] .custom-button {
    background-color: var(--tblr-dark, #1e293b);
    border-color: rgba(255, 255, 255, 0.1);
    color: var(--tblr-body-color, #cbd5e1);
}

[data-bs-theme="dark"] .custom-button:hover {
    background-color: var(--tblr-gray-800, #374151);
    border-color: var(--tblr-primary, #3b82f6);
    color: var(--tblr-primary, #3b82f6);
}

[data-bs-theme="dark"] .custom-button:active {
    background-color: var(--tblr-gray-700, #4b5563);
}

