<?php  

// if (!function_exists('load_rules')) {
//     function load_rules($group, array $rules = null) {
//         $validator = service('validation');
//         $existing_rules = $validator->getRuleGroup($group);
//         if (is_array($rules)) {
//             if($existing_rules){
//                 // Merge existing and provided rules (prioritize provided rules)
//                 $merged_rules = array_merge($rules, $existing_rules);
//             } else {
//                 $merged_rules = $rules;
//             }
//         } else {
//             $merged_rules = $existing_rules;
//         }
//         return $merged_rules ?: [];
//     }
// }

if (!function_exists('load_rules')) {
    function load_rules($group, array $rules = [], array $additional_rules = []) {
        $validator = service('validation');
        $existing_rules = $validator->getRuleGroup($group) ?? [];

        // Prioritize provided rules over existing rules
        $merged_rules = array_merge($existing_rules, $rules, $additional_rules);

        return $merged_rules;
    }
}

if(!function_exists('show_validation_error')){
    function show_validation_error($needle=null, $haystack=null){
    	if(is_array($haystack)){
    		return array_key_exists($needle, $haystack) ? $haystack[$needle] : false;
        }
      	return false;
    }
}


if(!function_exists('extractNoOfDays')){
    function extractNoOfDays($startDate, $noOfDays) {
        $eventDays = [];
        $date = date_create($startDate);
        for ($i = 0; $i < $noOfDays; $i++) {
            $eventDays[] = [
                'day' => 'Day '.$i + 1,
                'eventDate' => date_format($date, 'Y-m-d'),
            ];
            date_add($date, date_interval_create_from_date_string("1 day"));
        }
        return $eventDays;
    }
}
if(!function_exists('convert_to_mysql_date')){
    function convert_to_mysql_date($date_string) {
        $timestamp = strtotime($date_string);
        if ($timestamp === false) {
            return false; // Handle invalid date format
        }
        return date('Y-m-d', $timestamp);
    }
}


if(!function_exists('isInvalid')){
    function isInvalid($needle){
    	if(session()->has('errors')){
    		if(is_array(session("errors"))){
	    		return array_key_exists($needle, session("errors")) ? 'is-invalid' : false;
	        }
	        else{
	        	return 'valid';
	        }
    	}
    }
}


if(!function_exists('getActiveNav')){
    function getActiveNav($page){
        $currentPage = getCurrentPage();
        // Compare the current page with the expected page
        if ($currentPage == $page){
            return 'active'; // or any class name you prefer for highlighting
        }else{
            return '';
        }
    }
}



if(!function_exists('getCurrentPage')){
    function getCurrentPage(){
        $uri = service('uri');
        return $uri->getSegment(1) . '/' . $uri->getSegment(2);
    }
}

if(!function_exists('cleanAssocArr')){
    function cleanAssocArr($arr){
        $newArr = array();
        foreach($arr as $key=>$value){
            if($value!==''){
                $newArr[$key]=$value;
            }
        }
        return $newArr;
    }
}

//menu management

if (!function_exists('renderSidebarMenu')) {
    function renderSidebarMenu($userId=null) {
        try {
            $menuService = new \Modules\MenuManagement\Services\MenuService();
            return $menuService->renderSidebarMenu($userId);
        } catch (Exception $e) {
            // Log error and return empty string
            log_message('error', 'renderSidebarMenu error: ' . $e->getMessage());
            return '';
        }
    }
}

if (!function_exists('clearUserMenuCache')) {
    function clearUserMenuCache($userId) {
        try {
            $menuService = new \Modules\MenuManagement\Services\MenuService();
            $menuService->clearUserMenuCache($userId);
            log_message('debug', 'clearUserMenuCache helper - Cleared cache for user: ' . $userId);
        } catch (Exception $e) {
            log_message('error', 'clearUserMenuCache helper error: ' . $e->getMessage());
        }
    }
}

if (!function_exists('preGenerateUserMenus')) {
    function preGenerateUserMenus($userId) {
        try {
            $menuService = new \Modules\MenuManagement\Services\MenuService();
            return $menuService->preGenerateUserMenus($userId);
        } catch (Exception $e) {
            log_message('error', 'preGenerateUserMenus helper error: ' . $e->getMessage());
            return [];
        }
    }
}

if (!function_exists('getMenuBreadcrumb')) {
    function getMenuBreadcrumb($userId = null) {
        $menuService = new \Modules\MenuManagement\Services\MenuService();
        return $menuService->getBreadcrumb($userId);
    }
}

if (!function_exists('renderTablerSidebarMenu')) {
    function renderTablerSidebarMenu($userId = null) {
        try {
            $menuService = new \Modules\MenuManagement\Services\MenuService();
            $menus = $menuService->getUserMenus($userId);
            return buildTablerMenuHtml($menus);
        } catch (Exception $e) {
            // Log error and return empty string
            log_message('error', 'renderTablerSidebarMenu error: ' . $e->getMessage());
            return '';
        }
    }
}

if (!function_exists('buildTablerMenuHtml')) {
    function buildTablerMenuHtml($menus, $level = 0) {
        if (empty($menus)) {
            return '';
        }

        $html = '';

        foreach ($menus as $menu) {
            $hasChildren = isset($menu['children']) && !empty($menu['children']);
            $isActive = isMenuActive($menu['url']);
            $hasActiveChild = $hasChildren ? hasActiveChild($menu['children']) : false;
            $shouldBeOpen = $isActive || $hasActiveChild;

            if ($hasChildren) {
                // Parent menu with children (dropdown)
                $html .= '<li class="nav-item dropdown">';
                $html .= '<a class="nav-link dropdown-toggle' . ($shouldBeOpen ? ' show' : '') . '" href="#navbar-' . sanitizeForId($menu['label']) . '" data-bs-toggle="dropdown" data-bs-auto-close="false" role="button" aria-expanded="' . ($shouldBeOpen ? 'true' : 'false') . '">';
                $html .= '<span class="nav-link-icon d-md-none d-lg-inline-block">';
                $html .= convertIconToTabler($menu['icon']);
                $html .= '</span>';
                $html .= '<span class="nav-link-title">' . esc($menu['label']) . '</span>';
                $html .= '</a>';
                $html .= '<div class="dropdown-menu' . ($shouldBeOpen ? ' show' : '') . '">';
                $html .= '<div class="dropdown-menu-columns">';
                $html .= '<div class="dropdown-menu-column">';

                // Render children
                foreach ($menu['children'] as $child) {
                    $childIsActive = isMenuActive($child['url']);
                    $html .= '<a class="dropdown-item' . ($childIsActive ? ' active' : '') . '" href="' . site_url($child['url']) . '">';
                    $html .= esc($child['label']);
                    $html .= '</a>';
                }

                $html .= '</div>';
                $html .= '</div>';
                $html .= '</div>';
                $html .= '</li>';
            } else {
                // Single menu item
                $html .= '<li class="nav-item">';
                $html .= '<a class="nav-link' . ($isActive ? ' active' : '') . '" href="' . site_url($menu['url']) . '">';
                $html .= '<span class="nav-link-icon d-md-none d-lg-inline-block">';
                $html .= convertIconToTabler($menu['icon']);
                $html .= '</span>';
                $html .= '<span class="nav-link-title">' . esc($menu['label']) . '</span>';
                $html .= '</a>';
                $html .= '</li>';
            }
        }

        return $html;
    }
}

if (!function_exists('isMenuActive')) {
    function isMenuActive($menuUrl) {
        if (empty($menuUrl)) {
            return false;
        }

        $currentUrl = uri_string();

        // Exact match
        if ($currentUrl === $menuUrl) {
            return true;
        }

        // Check if current URL starts with menu URL (for parent menus)
        if (strpos($currentUrl, $menuUrl) === 0) {
            return true;
        }

        return false;
    }
}

if (!function_exists('hasActiveChild')) {
    function hasActiveChild($children) {
        foreach ($children as $child) {
            // Check if this child is active
            if (isMenuActive($child['url'])) {
                return true;
            }

            // Check if this child has active grandchildren
            if (isset($child['children']) && !empty($child['children'])) {
                if (hasActiveChild($child['children'])) {
                    return true;
                }
            }
        }

        return false;
    }
}

if (!function_exists('sanitizeForId')) {
    function sanitizeForId($string) {
        // Convert to lowercase and replace spaces/special chars with hyphens
        $string = strtolower($string);
        $string = preg_replace('/[^a-z0-9]+/', '-', $string);
        $string = trim($string, '-');
        return $string;
    }
}

if (!function_exists('convertIconToTabler')) {
    function convertIconToTabler($iconClass) {
        if (empty($iconClass)) {
            return '<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"/></svg>';
        }

        // Icon mapping from FontAwesome to Tabler Icons
        $iconMap = [
            'fas fa-tachometer-alt' => '<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M5 12l-2 0l9 -9l9 9l-2 0"/><path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7"/><path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6"/></svg>',
            'fas fa-users' => '<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M9 7m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0"/><path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/><path d="M21 21v-2a4 4 0 0 0 -3 -3.85"/></svg>',
            'fas fa-shield-alt' => '<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 3a12 12 0 0 0 8.5 3a12 12 0 0 1 -8.5 15a12 12 0 0 1 -8.5 -15a12 12 0 0 0 8.5 -3"/><path d="M9 12l2 2l4 -4"/></svg>',
            'fas fa-cogs' => '<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z"/><path d="M9 12a3 3 0 1 0 6 0a3 3 0 0 0 -6 0"/></svg>',
            'fas fa-envelope' => '<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M3 7a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v10a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-10z"/><path d="M3 7l9 6l9 -6"/></svg>',
            'fas fa-building' => '<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M3 21l18 0"/><path d="M5 21v-14l8 -4v18"/><path d="M19 21v-10l-6 -4"/><path d="M9 9l0 .01"/><path d="M9 12l0 .01"/><path d="M9 15l0 .01"/><path d="M9 18l0 .01"/></svg>',
            'fas fa-list' => '<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M9 6l11 0"/><path d="M9 12l11 0"/><path d="M9 18l11 0"/><path d="M5 6l0 .01"/><path d="M5 12l0 .01"/><path d="M5 18l0 .01"/></svg>',
            'fas fa-chart-bar' => '<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M3 12m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v6a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"/><path d="M9 8m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v10a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"/><path d="M15 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v14a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"/><path d="M4 20l14 0"/></svg>',
        ];

        // Return mapped icon or fallback to FontAwesome class with generic Tabler icon
        if (isset($iconMap[$iconClass])) {
            return $iconMap[$iconClass];
        }

        // Fallback: return a generic icon
        return '<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"/></svg>';
    }
}

?>